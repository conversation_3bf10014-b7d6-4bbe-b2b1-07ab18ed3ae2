name: Invalidate Build-Manager Cache on Tag Release

on:
  push:
    tags:
      - "v*" # Triggers on tags like v1.0.0, v2.3.1, etc.

jobs:
  call-api:
    runs-on: ubuntu-latest
    steps:
      - name: Invalidate Build manager tag cache
        run: |
          curl --location --request DELETE 'https://api.apptile.io/internal-build-system/api/flags/invalidate' \
            --header 'authorization: Bearer ${{ secrets.AUTH_BEARER }}' \
            --header 'x-api-token: ${{ secrets.X_API_TOKEN }}' \
            --header 'x-app-id: ${{ secrets.X_APP_ID }}'
