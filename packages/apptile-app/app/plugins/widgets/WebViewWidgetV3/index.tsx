import React, {useEffect, useState, useRef} from 'react';
import {StyleSheet, View, Platform, Text} from 'react-native';
import {WebView} from 'react-native-webview';
import _ from 'lodash';
import {PluginEditorsConfig} from '../../../common/EditorControlTypes';
import {
  datasourceTypeModelSel,
  getPlatformStyles,
  GetRegisteredPlugin,
  modelUpdateAction,
  PluginConfig,
  Selector,
  selectPluginConfig,
  TriggerActionIdentifier,
} from 'apptile-core';
import {EventTriggerIdentifier, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {defaultEditors, defaultStyleEditors} from 'apptile-core';
import {connectWidget} from 'apptile-core';
import docs from './docs';
import {handleEvent} from './eventsTransformer';
import {APPTILE_EVENTS_KEY} from './eventsTransformer/events';
import {useTheme} from 'apptile-core';
import DeviceInfo from 'react-native-device-info';
import snippets from './snippets.json';
import {runSaga} from 'redux-saga';
import {useSelector} from 'react-redux';
import axios from 'axios';

let base64ApptileWebTunnelSDK = '';
const webTunnelUrl = 'https://unpkg.com/@apptile/apptile-web-tunnel@latest/dist/index.min.js';
axios.get(webTunnelUrl).then(response => {
  if (response.data)
    base64ApptileWebTunnelSDK = 'data:application/javascript;base64,' + Buffer.from(response.data).toString('base64');
});

type WebViewWidgetConfigType = {
  value: string;
  loading: boolean;
  apptileTunnelLoading: boolean;
  onLoadEnd: string;
  headers: string;
  injectedJavaScript: string;
  injectedCss: string;
  cart: any;
  user: any;
  apptileDetails: any;
  reloadWebView: any;
};
const WebViewWidgetConfig: WebViewWidgetConfigType = {
  value: '',
  loading: true,
  apptileTunnelLoading: true,
  onLoadEnd: '',
  headers: '',
  injectedJavaScript: '',
  injectedCss: '',
  cart: '',
  user: '',
  apptileDetails: '',
  reloadWebView: 'action',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'webTunnelView',
  type: 'widget',
  name: 'Web Tunnel View',
  description: 'Load external uri in the app',
  layout: {
    flex: 1,
  },
  section: 'Display',
  icon: 'web-view',
};
let deviceUserAgent = '';
DeviceInfo.getUserAgent().then(userAgent => {
  deviceUserAgent = userAgent;
});
async function runGenerator(generatorFn, ...args) {
  let result;
  await runSaga({}, function* () {
    result = yield* generatorFn(...args);
  }).toPromise();
  return result;
}

const WebViewWidgetV3 = React.forwardRef((props, ref) => {
  const {model, modelUpdate, modelStyles, config, triggerEvent} = props;
  const value = model.get('value')?.toString();
  let html = model.get('html')?.toString();
  const htmlMode = model.get('useHtml');
  const [useHtml, setUseHtml] = useState(htmlMode);
  const snippetMode = model.get('snippetMode');
  const useAppTunnel = model.get('useAppTunnel');
  const userAgent = model.get('userAgent')?.toString().trim();
  const reloadKey = model.get('reloadKey');
  let componentProps = {};
  if (snippetMode) {
    componentProps = Object.fromEntries(
      Object.keys(model?.toJSON())
        .filter(e => e.startsWith('componentProps-'))
        .map(e => [e.slice(15), model.get(e)]),
    );
  }
  const {themeEvaluator} = useTheme();

  const headers = model.get('headers')?.toString();
  const injectJavaScriptBeforeContentLoad = model.get('injectJavaScriptBeforeContentLoad') ?? true;
  let injectedJavaScript = model.get('injectedJavaScript')?.toString() ?? '';
  let injectedCss = model.get('injectedCss')?.toString() ?? '';

  let injectedJavaScriptWhileContentIsLoading = model.get('injectedJavaScriptWhileContentIsLoading')?.toString() ?? '';

  const incognito = model.get('incognito');
  const domStorageEnabled = model.get('domStorageEnabled');
  const layout = config.get('layout');
  const layoutStyles = layout ? layout.getFlexProperties() : {flex: 1};
  const navigationalRegex = model.get('navigationalRegex');
  const defaultNavigationScreenName = model.get('defaultNavigationScreenName');
  const hideSelectors = model.get('hideSelectors')?.toString().trim();

  if (hideSelectors)
    injectedJavaScript = `
    const addThisSet2 = setInterval(()=>{
      if(document.getElementById('hideStyle1211-apptile')) clearInterval(addThisSet2);
      else{
        var hideStyle1211 = document.createElement('style');
        hideStyle1211.setAttribute('id', 'hideStyle1211-apptile');
        hideStyle1211.innerHTML = '${hideSelectors} { display: none !important;}';
        document.head.appendChild(hideStyle1211);
      }
    }, 10);`;
  if (useAppTunnel) {
    injectedJavaScriptWhileContentIsLoading = `
    const addThisSet1 = setInterval(()=>{
        if(document.getElementById('sc1211-apptile')) clearInterval(addThisSet1);
        else{
          if(!window.Apptile) {
            var sc1211 = document.createElement("script");
            sc1211.setAttribute("src", "${webTunnelUrl}");
            sc1211.setAttribute('id', 'sc1211-apptile');
            sc1211.setAttribute("type", "text/javascript");
            sc1211.setAttribute("fetchpriority", "high");
            document.head.appendChild(sc1211);
          }
        }
      }, 100);
      try{
        ${injectedJavaScriptWhileContentIsLoading}
      } catch(error) {
        window.Apptile.sendRawMessageAsync({type: 'POST', event: 'errorReport', eventData: error});
        console.error(error);
      }`;
    injectedJavaScript = `
    const addThisSet3 = setInterval(()=>{
        if(document.getElementById('sc1211-apptile')) clearInterval(addThisSet3);
        else{
          if(!window.Apptile) {
            var sc1211 = document.createElement("script");
            sc1211.setAttribute("src", "${webTunnelUrl}");
            sc1211.setAttribute('id', 'sc1211-apptile');
            sc1211.setAttribute("type", "text/javascript");
            sc1211.setAttribute("fetchprority", "high");
            document.head.appendChild(sc1211);
          }
        }
      }, 100);
    try{
      ${injectedJavaScript}
    } catch(error) {
      window.Apptile.sendRawMessageAsync({type: 'POST', event: 'errorReport', eventData: error});
      console.error(error);
    }`;
  }

  const {borderRadius, margin, padding, elevation: elevationStr, ...genericStyles} = modelStyles ?? {};
  const modelPlatformStyles = getPlatformStyles({...genericStyles, borderRadius, margin, padding});

  const onLoadEnd = () => {
    triggerEvent('onLoadEnd');
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  const onError = () => {
    modelUpdate([{selector: ['loading'], newValue: false}]);
  };

  // convert key-value pair input into an object
  const requestHeaders = headers ? Object.fromEntries(headers.split(',').map(header => header.split(':'))) : {};
  const webKey = useRef<string[]>([]);
  const webViewRef = useRef(null);
  const [webViewKey, setKey] = useState(1);
  const [source, setSource] = useState({uri: value, headers: requestHeaders});
  useEffect(() => {
    let newSource = {
      uri: value,
      headers: requestHeaders,
    };
    if (!_.isEqual(source, newSource)) {
      setSource(newSource);
      setKey(webViewKey + 1);
    } else if (reloadKey && webViewKey != reloadKey) {
      setKey(reloadKey);
    }
  }, [requestHeaders, source, value, webViewKey, reloadKey]);

  const sendMessageToIframe = (eventKey: string, status: 'success' | 'error', data: {[key: string]: any}) => {
    if (webViewRef?.current) {
      webViewRef?.current?.injectJavaScript(
        `window.Apptile.messageListener(${JSON.stringify({
          eventKey,
          status,
          result: {
            eventSDK: APPTILE_EVENTS_KEY.RECEIVE_KEY,
            type: 'POST',
            data,
          },
        })})`,
      );
    }
  };

  const datasource = model.get('queryDataSource');
  const dsConfig = useSelector(state => selectPluginConfig(state, null, datasource));
  const dsType = dsConfig?.subtype;
  const dsModelValues = useSelector(state => datasourceTypeModelSel(state, dsType));

  const runDatasourceQuery = async function (
    queryName: string,
    userInputVariables: any = {},
    callback: (props: {
      data: any;
      rawData: any;
      errors: any;
      hasError: any;
      hasNextPage: any;
      paginationMeta: any;
    }) => void,
  ) {
    if (!datasource) return {data: null, errors: ['Integration Not Active In App'], hasError: true};
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = dsModel?.getQueries();
    const querySchema = queries?.[queryName];

    if (!querySchema) return {data: null, errors: ['Invalid Request'], hasError: true};
    const {editableInputParams: inputVariables, transformer, cachePolicy} = querySchema;
    const mergedInputVariables = _.mergeWith({}, userInputVariables, inputVariables, (objValue, srcValue) => {
      if (typeof objValue === 'boolean' || typeof objValue === 'number') {
        return objValue;
      } else if (objValue === undefined) {
        return _.isEmpty(srcValue) ? undefined : srcValue;
      } else {
        return _.isEmpty(objValue) ? srcValue : objValue;
      }
    });
    const response = await runGenerator(
      dsModel.runQuery,
      dsModel,
      dsConfig,
      dsModelValues,
      queryName,
      mergedInputVariables,
      {
        transformers: [transformer],
        getNextPage: false,
        paginationMeta: undefined,
        cachePolicy: null,
      },
    );
    const {data, rawData, errors, hasError, hasNextPage, paginationMeta} = response;
    callback({data, rawData, errors, hasError, hasNextPage, paginationMeta});
  };

  const messageListener = (message: any) => {
    const rawMessageData = JSON.parse(message?.nativeEvent?.data || '{}');
    console.log('\n\n\n\n\n\nmessage', rawMessageData, '\n\n\n\n\n');
    const messageData = rawMessageData?.message;
    if (messageData && rawMessageData.eventKey) {
      handleEvent(
        rawMessageData.eventKey,
        messageData,
        sendMessageToIframe,
        triggerEvent,
        webKey,
        model,
        themeEvaluator,
        modelUpdate,
        runDatasourceQuery,
      );
    }
  };

  const handleNavigationStateChange = (navState: {
    url: string;
    isTopFrame: boolean;
    navigationType: string;
    mainDocumentURL: string;
  }) => {
    const {url, isTopFrame, navigationType} = navState;
    let {mainDocumentURL} = navState;
    if (Platform.OS === 'android') {
      mainDocumentURL = url;
    }
    // if (useHtml) {
    //   setUseHtml(false);
    // }
    if (Platform.OS !== 'android' && !isTopFrame && navigationType != 'click') return true;
    let found = false;
    console.log('request log 2', navState, source.uri);
    try {
      // Routing logic based on specific path patterns
      if (source.uri === mainDocumentURL) {
        return true;
      } else {
        if (mainDocumentURL) {
          const urlParts = mainDocumentURL?.split('?');
          const urlParams =
            urlParts.length > 1 ? Object.fromEntries(urlParts[1]?.split('&').map(e => e.split('='))) : {};
          const urlNodes = urlParts[0].split('/');
          for (let i in navigationalRegex) {
            const regex = new RegExp(
              navigationalRegex[i]?.expressionRaw?.regex,
              navigationalRegex[i]?.expressionRaw?.flags,
            );
            if (regex?.test(mainDocumentURL)) {
              console.log('navigationalScreenName', navigationalRegex[i].screenName);
              triggerEvent('navigationChanged', {
                screenName: navigationalRegex[i].screenName,
                urlNodes,
                urlParams,
                ...navState,
              });
              found = true;
            }
          }
          if (!found && defaultNavigationScreenName) {
            console.log('defaultNavigationScreenName', defaultNavigationScreenName, value, mainDocumentURL);
            triggerEvent('navigationChanged', {
              screenName: defaultNavigationScreenName,
              urlNodes,
              urlParams,
              ...navState,
            });
            found = true;
          }
          if (!found && useHtml) return true;
          if (!found) setSource({uri: mainDocumentURL, headers: requestHeaders});
        }
        return true;
      }
    } catch (error) {
      console.warn('Invalid URL format:', error);
      return true;
    }
  };
  console.log({uri: source.uri, useHtml});
  return (
    <View style={[layoutStyles, modelPlatformStyles]}>
      {source.uri || (html && useHtml) ? (
        <WebView
          key={webViewKey}
          onLoadEnd={onLoadEnd}
          onShouldStartLoadWithRequest={request => {
            console.log('log request', request);
            return handleNavigationStateChange(request);
          }}
          originWhitelist={['*']}
          source={
            useHtml
              ? {
                  html: (snippetMode && snippets[snippetMode]
                    ? `${'<style>' + injectedCss + '</style>'}<script>window.componentProps=${JSON.stringify(
                        componentProps,
                        null,
                        2,
                      )}</script>${snippets[snippetMode].prefix}${html}${snippets[snippetMode].suffix}`
                    : `<meta content="width=device-width, initial-scale=1" name="viewport" />${html} <style>${injectedCss}</style>`
                  )?.replace(webTunnelUrl, base64ApptileWebTunnelSDK),
                }
              : source
          }
          onError={onError}
          ref={webViewRef}
          onMessage={messageListener}
          incognito={incognito}
          domStorageEnabled={domStorageEnabled}
          userAgent={userAgent ? userAgent : deviceUserAgent}
          {...(injectedJavaScriptWhileContentIsLoading
            ? {injectedJavaScript: injectedJavaScriptWhileContentIsLoading}
            : {})}
          {...(injectedJavaScript && injectJavaScriptBeforeContentLoad
            ? {injectedJavaScriptBeforeContentLoaded: injectedJavaScript}
            : injectedJavaScript
            ? {injectedJavaScript}
            : {})}
          javaScriptEnabled={true}
        />
      ) : (
        <Text> Error Loading Widget </Text>
      )}
    </View>
  );
});

const widgetEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'headers',
      props: {
        label: 'Headers',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedJavaScript',
      props: {
        label: 'Injected JavaScript',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'injectedCss',
      props: {
        label: 'Injected CSS',
        placeholder: '',
      },
    },
    ...defaultEditors.basic,
  ],
  layout: defaultEditors.layout,
};

const propertySettings: PluginPropertySettings = {
  onLoadEnd: {
    type: EventTriggerIdentifier,
  },
  headers: {
    getValue: (model, renderedValue, selector) => {
      return renderedValue ? renderedValue.toString() : '';
    },
  },
  reloadWebView: {
    type: TriggerActionIdentifier,
    getValue() {
      return (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
        dispatch(
          modelUpdateAction([
            {
              selector: [...selector, 'reloadKey'],
              newValue: Date.now(),
            },
          ]),
        );
      };
    },
  },
};
const emptyOnupdate = null;

const styles = StyleSheet.create({
  loader: {
    bottom: 0,
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
  },
});

export default connectWidget('WebViewWidgetV3', WebViewWidgetV3, WebViewWidgetConfig, emptyOnupdate, widgetEditors, {
  propertySettings,
  widgetStyleConfig: defaultStyleEditors,
  pluginListing,
  docs,
});
