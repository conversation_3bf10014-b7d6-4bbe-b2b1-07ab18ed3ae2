/**
 * @format
 */

import {shouldInitWebLogrocket} from './web/globalvariables';
// import 'react-native-gesture-handler';
// import 'react-native-console-time-polyfill';
import {AppRegistry, Platform, Text, TextInput} from 'react-native';

import App from './app/App';
import PIPActivity from './PIPActivityRoot';
import { name as appName, pipactivityname } from './app.json';
import { triggerCustomEventListener } from 'apptile-core';
console.log('glvarcheck', shouldInitWebLogrocket);

/* ForDynamicType (Don't remove) Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.allowFontScaling = false; ForDynamicTypeEnd */

triggerCustomEventListener('markStart', 'pre_splash');
AppRegistry.registerComponent(appName, () => App);

if (Platform.OS === "android") {
  AppRegistry.registerComponent(pipactivityname, () => PIPActivity);
}
