import React, {useEffect, useRef, useState} from 'react';
import {useLocation, useMatch, useNavigate, useParams} from 'react-router';
import {Image, Pressable, StyleSheet, View, Text} from 'react-native';
import {selectScreensInNavWithPath} from '../selectors/EditorSelectors';
import Animated, {
  Easing,
  Extrapolation,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {
  editorSetActiveAttachmentId,
  editorSetActiveAttachmentKey,
  EDITOR_SELECTED_PAGE_TYPE,
  EDITOR_SELECT_NAV_COMPONENT,
} from '@/root/web/actions/editorActions';

import NavigationTree from '../components-v2/NavigationTree';
import Button from '../components-v2/base/Button';
import Tabs from '../components-v2/composite/Tabs';
import TilesExplorer from '../components-v2/TilesExplorer';
import MenuItem from '../components-v2/base/Menu';
import {AI_TILE_PAGE_ID, getMappedTileTagsForScreen, TileTagsNavigation} from '../common/tileConstants';
import {LeftPane as NotificationLeftPaneContent} from '@/root/web/views/notificationAdmin/shared/leftPane';
import {
  allAvailablePlans,
  deletePageConfig,
  initApptileIsEditable,
  MaterialCommunityIcons,
  navComponentDelete,
  selectModulesCache,
  SettingsConfig,
} from 'apptile-core';

import _, {pick} from 'lodash';
import BrandSettings from '@/root/web/views/settings/brand';
import {useDispatch, useSelector} from 'react-redux';
import {selectActiveScreen, selectSelectedPluginConfig} from '../selectors/EditorSelectors';
import {
  fetchAppIntegrations,
  fetchIntegrations,
  saveAppState,
  fetchIntegrationCategories,
  createNewAITile,
  editAITile,
} from '../actions/editorActions';
import {BuildManagerApi} from '../api/BuildApi';
import {useIsPreview} from 'apptile-core';
import TextElement from '../components-v2/base/TextElement';
import {EditorRootState} from '../store/EditorRootState';
import {changeOnboardingMetadata, fetchBrand} from '../actions/onboardingActions';
import {getOnboardingMetadataWithKey} from '../selectors/OnboardingSelector';
import {GO_TO_TILES_CLICKED} from '../common/onboardingConstants';
import {BrandSettingsTypes} from 'apptile-core';
const BRAND_EXPOSED_TILES = BrandSettingsTypes.BRAND_EXPOSED_TILES,
  BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY,
  BRAND_TILE_TAG = BrandSettingsTypes.BRAND_TILE_TAG,
  BRAND_TILE_TAG_DISPLAY = BrandSettingsTypes.BRAND_TILE_TAG_DISPLAY;
import {selectAppSettingsForKey} from 'apptile-core';
import {selectMandatoryCheck} from '../selectors/EditorModuleSelectors';
import {checkApptileEmailSelector, currentPlanFeaturesSelector} from '../selectors/FeatureGatingSelector';
import {Snapshots} from '../components-v2/Snapshots';
import {LanguageAndRegion} from '../components-v2/LanguageAndRegion';
import BlueprintsApi from '../api/BlueprintsApi';
import CodeInputControlV2 from '../components/controls-v2/CodeInputControl';
import AiTileLeftSideBar from './AITile/AiTileLeftSideBar';
import {generatePluginListingForModules} from '../views/pluginListing/components/PluginListingControl';
import Analytics from '@/root/web/lib/segment';
import NavigationEditor from '../components-v2/controls/NavigationEditor';
import Setting from '../components-v2/Setting';
import {builderEmails} from '../common/featureGatingConstants';

export const styles = StyleSheet.create({
  sidebarContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  sidebarLeft: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    zIndex: 10,
    borderRightWidth: 1,
    borderColor: '#E5E5E5',
    paddingHorizontal: 6,
  },
  sidebarHeader: {
    height: '8vh',
    minHeight: 'calc(2vh + 48px)',
    paddingVertical: '1vh',
    flexDirection: 'row',
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 36,
    marginHorizontal: 9,
    marginVertical: 11,
  },
  logoSmall: {
    width: 36,
    height: 36,
    marginHorizontal: 9,
    marginVertical: 11,
  },
  sidebarLeftSecondary: {
    position: 'absolute',
    top: 0,
    left: 80,
    bottom: 0,
    flex: 1,
    height: '100%',
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  sidebarContentContainer: {
    flex: 1,
  },
  leftSidebarWrapper: {width: 270},
  toTilesWrapper: {
    position: 'absolute',
    left: 80,
    top: 0,
    zIndex: -1,
    backgroundColor: '#F8FBF8',
    paddingHorizontal: 30,
    borderRadius: 21,
    height: 200,
    justifyContent: 'center',
    shadowColor: 'rgba(99, 99, 99, 0.45)',
    shadowOffset: {width: 0, height: 0},
    shadowRadius: 40,
  },
  tooltipMenuWrapper: {
    position: 'absolute',
    left: 0,
    top: -17,
    backgroundColor: '#000000',
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
    zIndex: 9,
    height: 25,
  },
  leftEditorSidebarText: {
    fontSize: 12,
  },
  loaderImage: {
    width: 60,
    height: 60,
  },
});

type LeftSidebarProps = {
  mainBar?: string;
  secondaryBar?: string;
};

const ToTilesModal = ({secondarySidebarZIndex}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const changeOnboarding = () => {
    dispatch(changeOnboardingMetadata({[GO_TO_TILES_CLICKED]: true}));
  };
  const onTileClicked = () => {
    navigate('../tiles');
    changeOnboarding();
  };

  const tileButtonClicked = useSelector((state: EditorRootState) =>
    getOnboardingMetadataWithKey(state, GO_TO_TILES_CLICKED),
  );

  const toTilesWrapperAnimation = useAnimatedStyle(() => ({
    transform: [{scale: secondarySidebarZIndex && !tileButtonClicked ? 1 : 0}],
    // display: secondarySidebarZIndex ? 'flex' : 'none',
    zIndex: secondarySidebarZIndex && !tileButtonClicked ? 9 : -1,
  }));

  return (
    <Animated.View style={[styles.toTilesWrapper, toTilesWrapperAnimation]}>
      <View style={{position: 'absolute', top: 53, left: -40}}>
        <MaterialCommunityIcons style={{fontSize: 70}} name={'menu-left'} color={'#F8FBF8'} />
      </View>
      <View style={{position: 'absolute', top: 20, right: 20}}>
        <Pressable onPress={changeOnboarding}>
          <MaterialCommunityIcons style={{fontSize: 20}} name={'close'} color={'#000000'} />
        </Pressable>
      </View>
      <TextElement color="SECONDARY" style={{marginBottom: 20}}>
        You are all set!
      </TextElement>
      <TextElement color="SECONDARY" style={{marginBottom: 20}}>
        Start building your app with{' '}
        <TextElement color="SECONDARY" style={{fontWeight: '700'}}>
          Tiles
        </TextElement>
      </TextElement>
      <Button onPress={onTileClicked} containerStyles={{height: 50, width: 200}} color="DEFAULT">
        Go to Tiles
      </Button>
    </Animated.View>
  );
};

const getShopPrimaryDomainReducer = (state: EditorRootState) => {
  return state.appModel?.getModelValue(['shopify', 'shop', 'primaryDomain', 'host']);
};

const LeftSidebar: React.FC<LeftSidebarProps> = props => {
  const screens: any = useSelector(selectScreensInNavWithPath(['/']));
  const {mainBar: currentMainBar, secondaryBar: currentSecondaryBar} = props;
  const match = useMatch('/dashboard/:orgId/app/:id');
  const {appId} = useSelector((state: EditorRootState) => state.apptile);
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const prevModuleConfig = useRef<Object | null>(null);

  const [mainBar, setMainBar] = React.useState(currentMainBar ?? '');
  const [secondaryBar, setSecondaryBar] = React.useState(currentSecondaryBar ?? '');

  //Closing the secondary bar based on the click of a tile once!
  if (prevModuleConfig.current === null && moduleConfig) {
    // Dispatch your action here
    if (secondaryBar === 'brand-settings') setSecondaryBar('');
    // Update the previous value ref
    prevModuleConfig.current = moduleConfig;
  }

  const appIntegrationIds = useSelector(state => state.integration.appIntegrationIds);
  const integrationsById = useSelector(state => state.integration.integrationsById);
  const appIntegrations = Object.values(pick(integrationsById, appIntegrationIds)).map(item => item.integrationCode);

  const navigate = useNavigate();
  const isPreview = useIsPreview();
  const params = useParams();
  const {panelName} = params;
  const [secondarySidebarZIndex, setSecondarySidebarZIndex] = useState(false);
  const sidebarLeftSecondaryTranslateX = useSharedValue(-450);
  const [hasTileAnimationEverHappened, setTileAnimationEverHappend] = useState<boolean>(false);
  const [isTileAnimationHappening, setTileAnimationStatus] = useState<boolean>(false);
  useEffect(() => {
    !secondaryBar && setSecondarySidebarZIndex(false);
    sidebarLeftSecondaryTranslateX.value = withTiming(
      !secondaryBar || isPreview ? -450 : 0,
      {
        duration: 680,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      },
      () => {
        secondaryBar && setSecondarySidebarZIndex(true);
      },
    );
  }, [isPreview, secondaryBar, sidebarLeftSecondaryTranslateX]);
  const sidebarLeftSecondaryAnimation = useAnimatedStyle(() => ({
    transform: [{translateX: sidebarLeftSecondaryTranslateX.value}],
    zIndex: secondarySidebarZIndex ? 9 : 0,
  }));

  const sidebarWidth = useSharedValue(270);
  useEffect(() => {
    sidebarWidth.value = withTiming(isPreview ? 0 : secondaryBar ? 80 : 270, {
      duration: 680,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPreview, secondaryBar, sidebarWidth]);
  const sidebarLeftAnimation = useAnimatedStyle(() => ({
    width: sidebarWidth.value,
    // paddingHorizontal: interpolate(sidebarWidth.value, [0, 80], {extrapolateRight: Extrapolation.CLAMP}),
  }));

  //Fetching brand
  const dispatch = useDispatch();
  const primaryDomain = useSelector(getShopPrimaryDomainReducer);
  useEffect(() => {
    if (!_.isEmpty(primaryDomain)) dispatch(fetchBrand(primaryDomain));
  }, [dispatch, primaryDomain]);

  const currentPageId = useSelector((state: EditorRootState) => state.activeNavigation?.activePageId);
  useEffect(() => {
    if (panelName != 'AI-Tile-Creation' && currentPageId == AI_TILE_PAGE_ID) {
      dispatch(navComponentDelete(['/', AI_TILE_PAGE_ID]));
      dispatch(deletePageConfig(AI_TILE_PAGE_ID));
      dispatch(initApptileIsEditable(true));
    }
  }, [panelName, currentPageId, dispatch]);

  return panelName != 'AI-Tile-Creation' ? (
    <View style={[styles.leftSidebarWrapper, secondaryBar ? {zIndex: 1} : {}]}>
      <View style={[styles.sidebarContainer, match ? {width: 340} : {}]}>
        <Animated.View style={[styles.sidebarLeft, sidebarLeftAnimation]}>
          {mainBar !== 'APP_EDITOR' && (
            <View style={[styles.sidebarHeader]}>
              <Pressable
                onPress={() => {
                  navigate(`/dashboard/${params.orgId}/app/${params.id}`);
                }}>
                {!secondaryBar ? (
                  <Image
                    style={styles.logo}
                    source={require('@/root/web/assets/images/logo.png')}
                    resizeMode="contain"
                  />
                ) : (
                  <Image
                    style={styles.logoSmall}
                    source={require('@/root/web/assets/images/apptile_icon.png')}
                    resizeMode="contain"
                  />
                )}
              </Pressable>
            </View>
          )}
          <LeftPaneContent
            mainBar={mainBar}
            setMainBar={setMainBar}
            secondaryBar={secondaryBar}
            setSecondaryBar={setSecondaryBar}
          />
        </Animated.View>
      </View>
      <Animated.View style={[styles.sidebarLeftSecondary, sidebarLeftSecondaryAnimation, {overflow: 'visible'}]}>
        <View
          style={[
            styles.sidebarContentContainer,
            {
              paddingRight: isTileAnimationHappening ? '8px' : '0px',
            },
            secondaryBar != 'tiles'
              ? {
                  overflow: 'scroll',
                }
              : {},
            secondaryBar === 'languageAndRegion'
              ? {
                  overflow: 'hidden',
                }
              : {},
            {width: secondaryBar === 'tiles' || secondaryBar === 'brand-settings' ? 330 : 260},
            {},
          ]}>
          {secondaryBar === 'pages' && <NavigationTree />}
          {secondaryBar === 'snapshots' && <Snapshots />}
          {secondaryBar === 'languageAndRegion' && <LanguageAndRegion />}

          {secondaryBar === 'tiles' && (
            <View style={{flex: 1}}>
              <AvailableTilesComponent
                appId={appId}
                appIntegrations={appIntegrations}
                appIntegrationObj={integrationsById}
                tileAnimation={{
                  hasTileAnimationEverHappened,
                  isTileAnimationHappening,
                  setTileAnimationEverHappend,
                  setTileAnimationStatus,
                }}
              />
            </View>
          )}
          {secondaryBar === 'brand-settings' && <BrandSettings />}
          {secondaryBar === 'navigation' && <NavigationEditor />}
          {secondaryBar === 'setting' && <Setting />}
        </View>
      </Animated.View>
      {secondaryBar === 'brand-settings' && <ToTilesModal secondarySidebarZIndex={secondarySidebarZIndex} />}
    </View>
  ) : (
    <AiTileLeftSideBar
      mainBar={mainBar}
      setMainBar={setMainBar}
      secondaryBar={secondaryBar}
      setSecondaryBar={setSecondaryBar}
    />
  );
};

const AvailableTilesComponent = ({appId, appIntegrations, appIntegrationObj, tileAnimation}: any) => {
  const dispatch = useDispatch();
  const [showAITileButton, setShowAITileButton] = useState(false);

  useEffect(() => {
    dispatch(fetchAppIntegrations(appId));
    dispatch(fetchIntegrations());
    dispatch(fetchIntegrationCategories());

    const fetchBuilds = async () => {
      try {
        const response = await BuildManagerApi.getBuildsV2(appId);
        const builds = response.data || [];

        // If there are no builds, show the button
        if (builds.length === 0) {
          setShowAITileButton(true);
          return;
        }

        // Check if any build was created before April 30th, 2025
        const cutoffDate = new Date('2025-04-30');

        // Check if any build (iOS or Android) was created before the cutoff date
        const anyBuildBeforeCutoff = builds.some(build => new Date(build.createdAt) > cutoffDate);

        // Only show the button if there are no builds before the cutoff date
        setShowAITileButton(anyBuildBeforeCutoff);
      } catch (error) {
        console.error('Error fetching builds:', error);
        // In case of error, default to hiding the button
        setShowAITileButton(true);
      }
    };

    fetchBuilds();
  }, [appId]);

  const activeScreen = useSelector(selectActiveScreen);
  const navigate = useNavigate();

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const [searchQuery, setSearchQuery] = useState('');
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const brandSpecificTag = brandSettings.getSettingValue(BRAND_TILE_TAG) ?? '';
  const brandSpecificTagDisplay = brandSettings.getSettingValue(BRAND_TILE_TAG_DISPLAY) ?? '';
  const brandExposedTiles = brandSettings.getSettingValue(BRAND_EXPOSED_TILES) ?? '';
  const [exposedModules, setExposedModules] = useState<string[]>(brandExposedTiles?.split(',')?.filter(e => e) ?? []);
  const [screenBasedTiles, setScreenBasedTiles] = useState<any>();
  const modulesCache = useSelector(state => selectModulesCache(state));
  const allPlugins = generatePluginListingForModules(modulesCache);
  const aiPlugins = useSelector(state => {
    return allPlugins
      ?.filter(
        e =>
          (state.appConfig?.current?.modules?.get(e.moduleUUID)?.isAITile ?? false) &&
          e?.name != 'Apptile AI Tile Base' &&
          e?.name != 'AI Tile New',
      )
      ?.map(e => state.appConfig?.current?.modules?.get(e.moduleUUID));
  });
  const activeBlueprintUUID = useSelector((state: EditorRootState) => state.appConfig.current?.blueprintUUID);
  const appConfigLoaded = useSelector(
    (state: EditorRootState) => !!state.appConfig.current?.title && state.appConfig?.isFetched,
  );
  const blueprintDetails = useRef<Object | null>(null);
  const [blueprintFetched, setBlueprintFetched] = useState<boolean>(!!blueprintDetails.current?.slug);
  useEffect(() => {
    if (appConfigLoaded && !activeBlueprintUUID) {
      blueprintDetails.current = {slug: 'skandi'};
      setBlueprintFetched(true);
    }
    if (
      appConfigLoaded &&
      activeBlueprintUUID &&
      typeof activeBlueprintUUID === 'string' &&
      blueprintDetails?.current?.id != activeBlueprintUUID
    ) {
      BlueprintsApi.getBlueprint(activeBlueprintUUID).then((resp: any) => {
        if (resp.data) {
          blueprintDetails.current = resp.data;
          setTimeout(() => setBlueprintFetched(true), 100);
        } else {
          blueprintDetails.current = null;
        }
      });
    }
  }, [activeBlueprintUUID, appConfigLoaded]);
  useEffect(() => {
    setExposedModules(brandExposedTiles?.split(',')?.filter(e => e) ?? []);
  }, [brandExposedTiles]);

  let [tileTags, setTileTags] = useState({});

  useEffect(() => {
    let mappedTiles = {};
    if (activeScreen && activeScreen[0] != null && activeScreen[0].name != null) {
      mappedTiles = getMappedTileTagsForScreen(activeScreen[0].name);
      setScreenBasedTiles(mappedTiles);
    }
    if (_.isEmpty(mappedTiles)) {
      mappedTiles = TileTagsNavigation;
      if (brandSpecificTag && brandSpecificTagDisplay && brandSpecificTag.trim() && brandSpecificTagDisplay.trim()) {
        mappedTiles = {...{[brandSpecificTag.trim()]: brandSpecificTagDisplay.trim()}, ...mappedTiles};
      }
      if (exposedModules.length > 0) {
        mappedTiles = {
          ...{exposedTiles: 'My Tiles'},
          ...mappedTiles,
        };
      }
    } else {
      if (brandSpecificTag && brandSpecificTagDisplay && brandSpecificTag.trim() && brandSpecificTagDisplay.trim()) {
        mappedTiles = {...mappedTiles, ...{[brandSpecificTag.trim()]: brandSpecificTagDisplay.trim()}};
      }
      if (exposedModules.length > 0) {
        mappedTiles = {
          ...mappedTiles,
          ...{exposedTiles: 'My Tiles'},
        };
      }
    }

    if (aiPlugins.length > 0) {
      mappedTiles = {
        ...{aiTiles: 'AI Tiles'},
        ...mappedTiles,
      };
    }
    if (blueprintDetails.current?.slug) {
      mappedTiles = {...{themeTiles: 'Theme Tiles'}, ...mappedTiles};
    } else {
      mappedTiles = {...mappedTiles};
    }
    setTileTags(mappedTiles);
    setScreenBasedTiles(mappedTiles);
  }, [activeScreen, exposedModules, aiPlugins.length, blueprintDetails.current?.slug, setTileTags]);

  const onAITileCreation = () => {
    dispatch(createNewAITile('React'));
    navigate('../AI-Tile-Creation');
    Analytics.track('editor:editor_AiTile_OnTileCreationClick');
  };

  return (
    <>
      <View style={{paddingHorizontal: 16, paddingTop: 8, gap: 8, flex: 1}}>
        <CodeInputControlV2
          placeholder={'Search Tiles...'}
          singleLine
          value={searchQuery}
          onChange={_.debounce(setSearchQuery, 300)}
        />
        <Tabs
          rootStyles={{flex: 1, flexBasis: 'auto'}}
          currentTab={searchQuery ? tileTags.themeTiles : Object.keys(tileTags)[0]}
          tabs={Object.keys(tileTags).map(tag => ({
            title: tileTags[tag],
            disableScroll: true,
            component: (
              <>
                {searchQuery ? (
                  <TilesExplorer
                    tags={['themeTiles']}
                    appId={appId}
                    themeSlug={blueprintDetails.current?.slug}
                    appIntegrations={appIntegrations}
                    appIntegrationObj={appIntegrationObj}
                    tileAnimation={tileAnimation}
                    screenBasedTiles={screenBasedTiles}
                    legacyMode={false}
                    searchQuery={searchQuery}
                  />
                ) : blueprintFetched ? (
                  <TilesExplorer
                    tags={[tag]}
                    appId={appId}
                    aiPlugins={aiPlugins}
                    themeSlug={blueprintDetails.current?.slug}
                    appIntegrations={appIntegrations}
                    appIntegrationObj={appIntegrationObj}
                    tileAnimation={tileAnimation}
                    screenBasedTiles={screenBasedTiles}
                    legacyMode={false}
                  />
                ) : (
                  <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                    <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} />
                  </View>
                )}
              </>
            ),
          }))}
          noOfLines={2}
          activeVariant="FILLED"
          inactiveVariant="FILLED"
          activeColor="PRIMARY"
          inactiveColor="NEW_TAB"
          activeOpaque={true}
          inactiveBackgroundColor="NEW_TAB"
          size="EXTRA-SMALL"
          key={`tiles-blueprint-fetched-${blueprintFetched}-${searchQuery.length}`}
        />
      </View>
      {showAITileButton && (
        <View style={{width: '100%', padding: 10, borderTopColor: '#e5e5e5', borderTopWidth: 1}}>
          <Button variant={'FILLED-PILL'} color={'CTA'} onPress={onAITileCreation}>
            Create tiles with AI
          </Button>
        </View>
      )}
    </>
  );
};

type LeftPaneContentProps = {
  mainBar: string;
  setMainBar: (val: string) => void;
  secondaryBar: string;
  setSecondaryBar: (val: string) => void;
};

const LeftPaneContent: React.FC<LeftPaneContentProps> = props => {
  const {mainBar} = props;

  switch (mainBar) {
    case 'DASHBOARD':
      return <DashBoardLeftPaneContent {...props} />;
    case 'MANUAL_NOTIFICATION_PLAYGROUND':
      return <NotificationLeftPaneContent />;
    case 'AUTOMATED_NOTIFICATION_PLAYGROUND':
      return <NotificationLeftPaneContent isAutomatedCampaign />;
    case 'NOTIFICATION':
      return <NotificationLeftPaneContent />;
    case 'APP_EDITOR':
      return <AppEditorLeftPaneContent {...props} />;
    case 'APP_SETTINGS':
      return <SettingsLeftPaneContent {...props} />;
    default:
      return <></>;
  }
};

type DashBoardLeftPaneContentProps = {} & LeftPaneContentProps;

const DashBoardLeftPaneContent: React.FC<DashBoardLeftPaneContentProps> = props => {
  const {setSecondaryBar} = props;

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const params = useParams();
  const isApptileEmail = useSelector(checkApptileEmailSelector) ?? false;
  const {user} = useSelector((state: EditorRootState) => state.user);

  const [activeItem, setActiveItem] = React.useState<string>('store');

  return (
    <View style={[styles.sidebarContentContainer, {overflowX: 'hidden'}]}>
      <MenuItem
        id={'store'}
        text={'Dashboard'}
        icon={'house-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'store'}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('store');
          navigate('store');
        }}
      />
      <MenuItem
        id={'design'}
        text={'Your Design'}
        icon={'your-design'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'design'}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('design');
          navigate(`./../tiles`);
        }}
      />
      <MenuItem
        id={'notification'}
        text={'Notifications'}
        icon={'bell-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'notification'}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('notification');
          navigate(`notifications`);
        }}
      />
      <MenuItem
        id={'analytics'}
        text={'Analytics'}
        icon={'analytics-outline'}
        iconType="ApptileWebIcons"
        premiumBase={allAvailablePlans.PLUS}
        isActive={activeItem === 'analytics'}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('analytics');
          navigate(`analytics`);
        }}
      />
      <MenuItem
        id={'integrations'}
        text={'Integrations'}
        icon={'download-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'integrations'}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('integrations');
          navigate(`integrations`);
        }}
      />
      <MenuItem
        id={'live'}
        text={'Live'}
        icon={'live-tv'}
        iconType="MaterialIcons"
        isActive={activeItem === 'live'}
        premiumBase={allAvailablePlans.PRO_LIVE_SELLING}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('live');
          navigate(`live`);
        }}
      />
      <MenuItem
        id={'themes'}
        text={'Themes'}
        icon={'themes'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'themes'}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('themes');
          navigate(`themes`);
        }}
      />
      <MenuItem
        id={'settings'}
        text={'Settings'}
        icon={'gear-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'settings'}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('settings');
          navigate(`settings`);
        }}
      />

      <MenuItem
        id={'publishFlow'}
        text={'Build & Publish'}
        icon={'phone-android'}
        iconType="MaterialIcons"
        isActive={activeItem === 'publishFlow'}
        onPress={() => {
          navigate(
            `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/dashboard/publish`,
          );
          setSecondaryBar('');
          setActiveItem('publishFlow');
        }}
      />

      <MenuItem
        id={'resources'}
        text={'Resources'}
        icon={'book-outline'}
        iconType="ApptileWebIcons"
        hasSubMenu={true}
        subMenuItems={[
          {
            id: 'support',
            text: 'Help',
            onPress: () => {
              window.open('https://apptile.com/help/', '_blank');
              setSecondaryBar('');
              setActiveItem('support');
            },
            isActive: activeItem === 'support',
            hasSubMenu: false,
          },
          {
            id: 'whatsNew',
            text: "What's New",
            onPress: () => {
              window.open('https://apptile.com/product-updates', '_blank');
              setSecondaryBar('');
              setActiveItem('whatsNew');
            },
            isActive: activeItem === 'whatsNew',
            hasSubMenu: false,
          },
        ]}
      />
      {isApptileEmail && (
        <>
          <MenuItem
            id={'build'}
            text={'Build'}
            icon={'book-outline'}
            iconType="ApptileWebIcons"
            isActive={activeItem === 'build'}
            onPress={() => {
              navigate(
                `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/dashboard/builds`,
              );
              setSecondaryBar('');
              setActiveItem('build');
            }}
          />
          <MenuItem
            id={'legacy'}
            text={'Studio'}
            icon={'book-outline'}
            iconType="ApptileWebIcons"
            isActive={activeItem === 'legacy'}
            onPress={() => {
              window.open(
                `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/studio`,
                '_self',
              );
              setSecondaryBar('');
              setActiveItem('legacy');
            }}
          />
        </>
      )}
      {(isApptileEmail || builderEmails.includes(user?.email)) && (
        <>
          <MenuItem
            id={'builder'}
            text={'Builder UI'}
            icon={'book-outline'}
            iconType="ApptileWebIcons"
            isActive={activeItem === 'builder'}
            onPress={() => {
              window.open(
                `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/builder`,
                '_self',
              );
              setSecondaryBar('');
              setActiveItem('builder');
            }}
          />
          {!isApptileEmail && (
            <MenuItem
              id={'build'}
              text={'Build'}
              icon={'book-outline'}
              iconType="ApptileWebIcons"
              isActive={activeItem === 'build'}
              onPress={() => {
                navigate(
                  `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/dashboard/builds`,
                );
                setSecondaryBar('');
                setActiveItem('build');
              }}
            />
          )}
        </>
      )}
    </View>
  );
};

type AppEditorLeftPaneContentProps = {} & LeftPaneContentProps;

const availableAppEditorPanels = [
  'tiles',
  'pages',
  'navigation',
  'brand-settings',
  'languageAndRegion',
  'snapshots',
  'setting',
];

const AppEditorLeftPaneContent: React.FC<AppEditorLeftPaneContentProps> = props => {
  const {secondaryBar, setSecondaryBar} = props;
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();
  const forks = useSelector(state => state.forks);

  const {panelName} = params;

  const [activeItem, setActiveItem] = React.useState<string>();

  useEffect(() => {
    if (panelName && _.includes(availableAppEditorPanels, panelName)) {
      setActiveItem(panelName);
      setSecondaryBar(panelName);
    } else {
      setSecondaryBar('');
      setActiveItem('');
    }
  }, [setSecondaryBar, location.pathname, location, panelName]);

  useEffect(() => {
    if (secondaryBar && panelName !== secondaryBar)
      navigate(
        `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/${secondaryBar}`,
      );
  }, [secondaryBar]);

  const mandatoryFields = useSelector(selectMandatoryCheck());
  const isApptileEmail = useSelector(checkApptileEmailSelector) ?? false;
  const {user} = useSelector((state: EditorRootState) => state.user);
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isFeatureDisabled = !currentPlanFeatures.includes(allAvailablePlans.ENTERPRISE);
  const isMultiLanguageDisabled = !currentPlanFeatures.includes(allAvailablePlans.PRO);

  const availableForkIds = forks?.appForkIds;

  return (
    <View style={[styles.sidebarContentContainer, {overflow: 'hidden'}]}>
      {/* <View style={{marginLeft: 6}}>
        <MenuItem
          id={'dashboard'}
          text={'Dashboard'}
          icon={'grid-outline'}
          iconType="Ionicons"
          isActive={false}
          textStyles={styles.leftEditorSidebarText}
          onPress={() => {
            setActiveItem('');
            navigate(
              `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/dashboard/store`,
            );
          }}
        />
      </View> */}
      <MenuItem
        id={'dashboard'}
        text={'Dashboard'}
        icon={'grid-outline'}
        iconType="Ionicons"
        isActive={activeItem === 'dashboard'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('');
          navigate(
            `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/dashboard/store`,
          );
        }}
      />
      <MenuItem
        id={'tiles'}
        text={'Tiles'}
        icon={'tiles'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'tiles'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('tiles');
          setSecondaryBar('tiles');
        }}
      />

      <MenuItem
        id={'pages'}
        text={'Pages'}
        icon={'pages-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'pages'}
        redDot={mandatoryFields?.check ?? false}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('pages');
          setSecondaryBar('pages');
        }}
      />
      <MenuItem
        id={'navigation'}
        text={'Navigation'}
        icon={'menu'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'navigation'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('navigation');
          setSecondaryBar('navigation');
        }}
      />

      <MenuItem
        id={'brand-settings'}
        text={'Brand'}
        icon={'brands-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'brand-settings'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('brand-settings');
          setSecondaryBar('brand-settings');
        }}
      />

      {(isApptileEmail || !isMultiLanguageDisabled) && (
        <MenuItem
          id={'languageAndRegion'}
          text={'Languages'}
          icon={'language-outline'}
          iconType="Ionicons"
          isActive={activeItem === 'languageAndRegion'}
          vertical={!!secondaryBar}
          textStyles={styles.leftEditorSidebarText}
          onPress={() => {
            setActiveItem('languageAndRegion');
            setSecondaryBar('languageAndRegion');
          }}
        />
      )}

      {(isApptileEmail || !isFeatureDisabled) && (
        <MenuItem
          id={'snapshots'}
          text={'Version'}
          icon={'snapshots-outline'}
          iconType="ApptileWebIcons"
          isActive={activeItem === 'snapshots'}
          vertical={!!secondaryBar}
          textStyles={styles.leftEditorSidebarText}
          onPress={() => {
            setActiveItem('snapshots');
            setSecondaryBar('snapshots');
          }}
        />
      )}

      <MenuItem
        id={'setting'}
        text={'Settings'}
        icon={'gear-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'setting'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('setting');
          setSecondaryBar('setting');
        }}
      />
    </View>
  );
};

type SettingsLeftPaneContentProps = {} & LeftPaneContentProps;

const SettingsLeftPaneContent: React.FC<SettingsLeftPaneContentProps> = props => {
  const {secondaryBar, setSecondaryBar} = props;
  const location = useLocation();
  const navigate = useNavigate();

  const [activeItem, setActiveItem] = React.useState<string>('');

  useEffect(() => {
    setSecondaryBar('');
    setActiveItem('');
  }, [setSecondaryBar, location.pathname, location]);

  const params = useParams();
  const customNavigate = path => {
    if (params.planId) {
      navigate('./../../../' + path);
    } else {
      navigate('./../' + path);
    }
  };

  return (
    <View style={[styles.sidebarContentContainer, {overflow: 'hidden'}]}>
      <MenuItem
        id={'settings'}
        text={'Settings'}
        icon={'back-arrow'}
        iconType="ApptileWebIcons"
        isActive={false}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('');
          customNavigate('');
        }}
      />
      <MenuItem
        id={'yourAccount'}
        text={'Your account'}
        icon={'account-cog'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'yourAccount'}
        onPress={() => {
          customNavigate('your-account');
          setActiveItem('yourAccount');
        }}
      />
      <MenuItem
        id={'billingAccount'}
        text={'Billing account'}
        icon={'point-of-sale'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'billingAccount'}
        onPress={() => {
          customNavigate('billing');
          setActiveItem('billingAccount');
        }}
      />
      {/* <MenuItem
        id={'appListing'}
        text={'App Listing'}
        icon={'rocket-launch'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'appListing'}
        onPress={() => {
          customNavigate('app-listing');
          setActiveItem('appListing');
        }}
      /> */}
    </View>
  );
};

export default LeftSidebar;
