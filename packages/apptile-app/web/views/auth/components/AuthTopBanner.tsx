import Box from '@/root/web/components-v2/base/Box';
import React from 'react';
import {Image, StyleSheet} from 'react-native';

const AuthTopBanner: React.FC<{style: any}> = ({style}) => {
  return (
    <Box style={style}>
      <Image resizeMode="contain" style={styles.iconImage} source={require('../../../assets/images/logo.png')} />
    </Box>
  );
};

const styles = StyleSheet.create({
  iconImage: {
    width: 250,
    height: 32,
    margin: 4,
    alignSelf: 'center',
  },
});

export default AuthTopBanner;
