import React from 'react';
import {View, Text, StyleSheet, Pressable, Linking, ScrollView, ActivityIndicator} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import UploaderApi from '@/root/web/api/UploaderApi';
import {BackButton, Title, Button} from './shared';
import {useParams} from '@/root/web/routing.web';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {isEmpty, keyBy, get} from 'lodash';
import {useBuildPlatformContext} from './context';
import ButtonV2 from '../../components-v2/base/Button';
import {makeToast} from '../../actions/toastActions';
import {useDispatch} from 'react-redux';
import _ from 'lodash';

type IAssets = {
  setScreen: (v: 'SETTINGS' | 'ASSETS' | 'SECRETS' | 'BUILD_CONFIG') => void;
};

type IAssetsResponse = {
  id: string;
  appId: string;
  uploaderKey: string;
  fileName: string;
  status: string;
  assetClass: string;
  createdAt: Date;
  deletedAt: Date | null;
  url: string;
};

// Todo:  File asset need to polish
export const Assets: React.FC<IAssets> = props => {
  const param = useParams();
  const {record, updateRecord, loadingStates, setLoadingState} = useBuildPlatformContext();
  const dispatch = useDispatch();
  const [autoGeneratorLoading, setAutoGeneratorLoading] = React.useState(false);
  const [notificationDisabled, setNotifDisabled] = React.useState(false);
  const [isInitialLoad, setIsInitialLoad] = React.useState(true);

  const [assets, setAssets] = React.useState(
    record.platform === 'android'
      ? {
          splash: '',
          icon: '',
          notificationIcon: '',
          androidFirebaseServiceFile: '',
          androidStoreFile: '',
          firebaseServiceAccountKeyFile: '',
          iosFirebaseServiceFile: '',
          androidKeyStorePemFile: '',
        }
      : {
          iosFirebaseServiceFile: '',
        },
  );

  const [assetsUrls, setAssetsUrls] = React.useState(
    record.platform === 'android'
      ? {
          splash: null,
          icon: null,
          androidFirebaseServiceFile: null,
          androidStoreFile: null,
          firebaseServiceAccountKeyFile: null,
          iosFirebaseServiceFile: null,
          androidKeyStorePemFile: null,
          notificationIcon: null,
        }
      : {
          iosFirebaseServiceFile: null,
        },
  );

  const updateAssets = (key: string) => {
    return (value: string | string[]) => {
      setAssets(prev => {
        return {...prev, [key]: value};
      });
    };
  };

  const autoGenerateAndroidStoreFile = async () => {
    const appID = param.id as string;
    setAutoGeneratorLoading(true);
    try {
      await BuildManagerApi.generateAppAsset(appID, 'androidStoreFile');
      await fetchConfig();
    } catch (err) {
      console.error(err);
      dispatch(
        makeToast({
          content: 'Unable to create Android store file',
          appearances: 'error',
        }),
      );
    } finally {
      setAutoGeneratorLoading(false);
    }
  };

  const fetchConfig = async () => {
    try {
      setLoadingState('assets', true);
      const appID = param.id as string;
      if (isEmpty(appID)) {
        setLoadingState('assets', false);
        return;
      }

      const response = await BuildManagerApi.getActiveAppAsset<IAssetsResponse[]>(appID);
      const mappedResponse = keyBy(response.data, 'assetClass');
      const pickedUrls =
        record.platform === 'android'
          ? {
              splash: get(mappedResponse, ['splash', 'url'], ''),
              icon: get(mappedResponse, ['icon', 'url'], ''),
              androidFirebaseServiceFile: get(mappedResponse, ['androidFirebaseServiceFile', 'url'], ''),
              androidStoreFile:
                get(mappedResponse, ['androidStoreFile', 'url']) ||
                get(mappedResponse, ['androidStoreFile', 'url'], ''),
              firebaseServiceAccountKeyFile: get(mappedResponse, ['firebaseServiceAccountKeyFile', 'url'], ''),
              androidKeyStorePemFile: get(mappedResponse, ['androidKeyStorePemFile', 'url'], ''),
              notificationIcon: get(mappedResponse, ['notificationIcon', 'url'], ''),
            }
          : {
              iosFirebaseServiceFile: get(mappedResponse, ['iosFirebaseServiceFile', 'url'], ''),
              appStorePrivateKey: get(mappedResponse, ['appStorePrivateKey', 'url'], ''),
            };
      const pickedVal =
        record.platform === 'android'
          ? {
              splash: get(mappedResponse, ['splash', 'uploaderKey'], ''),
              icon: get(mappedResponse, ['icon', 'uploaderKey'], ''),
              notificationIcon: get(mappedResponse, ['notificationIcon', 'uploaderKey'], ''),
              androidFirebaseServiceFile: get(mappedResponse, ['androidFirebaseServiceFile', 'uploaderKey'], ''),
              androidStoreFile:
                get(mappedResponse, ['androidStoreFile', 'uploaderKey']) ||
                get(mappedResponse, ['androidStoreFile', 'url'], ''),
              firebaseServiceAccountKeyFile: get(mappedResponse, ['firebaseServiceAccountKeyFile', 'uploaderKey'], ''),
              androidKeyStorePemFile: get(mappedResponse, ['androidKeyStorePemFile', 'uploaderKey'], ''),
            }
          : {
              iosFirebaseServiceFile: get(mappedResponse, ['iosFirebaseServiceFile', 'uploaderKey'], ''),
              appStorePrivateKey: get(mappedResponse, ['appStorePrivateKey', 'uploaderKey'], ''),
            };
      setAssets(pickedVal);
      setAssetsUrls(pickedUrls);
      setIsInitialLoad(false);
    } catch (err) {
      console.error('Error fetching assets:', err);
      dispatch(
        makeToast({
          content: 'Failed to load assets configuration. Please try again.',
          appearances: 'error',
          duration: 5000,
        }),
      );
    } finally {
      setLoadingState('assets', false);
    }
  };

  async function enableIOSNotification(migrateToOneSignal: boolean) {
    const appId = param.id as string;
    try {
      setLoadingState('assets', true);
      const response = await BuildManagerApi.enableIOSNotification(appId, migrateToOneSignal);

      dispatch(
        makeToast({
          content: `${response.data}`,
          appearances: 'success',
        }),
      );

      setNotifDisabled(true);
    } catch (error) {
      console.log(`Error While Enabling IOS Notification in Build Dashboard`, error?.message);
      dispatch(
        makeToast({
          content: `Error While Enabling IOS Notification ${error?.message}`,
          appearances: 'error',
        }),
      );
    } finally {
      setLoadingState('assets', false);
    }
  }

  React.useEffect(() => {
    fetchConfig();
  }, [param.id, record.platform]);

  const isLoading = loadingStates.assets;

  return (
    <ScrollView style={styles.card}>
      <BackButton onPress={() => !isLoading && props.setScreen('SETTINGS')} disabled={isLoading} />
      <Title content="Assets" />

      {isLoading && isInitialLoad && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0075F2" />
          <Text style={styles.loadingText}>Loading assets configuration...</Text>
        </View>
      )}

      <View style={{flexDirection: 'row'}}>
        <Button
          onPress={() => !isLoading && updateRecord('platform', 'android')}
          text="Android"
          isDisabled={isLoading}
        />
        <Button
          onPress={() => !isLoading && updateRecord('platform', 'ios')}
          style={{marginLeft: 10}}
          text="IOS"
          isDisabled={isLoading}
        />
      </View>

      {record.platform === 'ios' ? (
        <>
          <InputBox
            name="Ios Firebase Service"
            assetClass="iosFirebaseServiceFile"
            fileType="plist"
            value={assets.iosFirebaseServiceFile}
            url={assetsUrls.iosFirebaseServiceFile}
            onChange={updateAssets('iosFirebaseServiceFile')}
            disabled={isLoading}
          />
          <InputBox
            name="AppStore Private Key"
            assetClass="appStorePrivateKey"
            fileType="p8"
            value={assets.appStorePrivateKey}
            url={assetsUrls.appStorePrivateKey}
            onChange={updateAssets('appStorePrivateKey')}
            disabled={isLoading}
          />
          <Button
            onPress={() => !isLoading && enableIOSNotification(false)}
            isDisabled={notificationDisabled || isLoading}
            text={isLoading ? 'Processing...' : 'Enable IOS Notification'}
          />
          <Button
            onPress={() => !isLoading && enableIOSNotification(true)}
            isDisabled={notificationDisabled || isLoading}
            text={isLoading ? 'Processing...' : 'Migrate to Onesignal'}
          />
        </>
      ) : (
        <>
          <InputBox
            name="Icon"
            value={assets.icon}
            url={assetsUrls.icon}
            onChange={updateAssets('icon')}
            assetClass="icon"
            fileType="image/png"
            width={1024}
            height={1024}
            disabled={isLoading}
          />
          <InputBox
            name="Splash"
            value={assets.splash}
            url={assetsUrls.splash}
            onChange={updateAssets('splash')}
            assetClass="splash"
            fileType="image/png,image/gif"
            disabled={isLoading}
          />
          <InputBox
            name="Notification Icon"
            value={assets.notificationIcon}
            onChange={updateAssets('notificationIcon')}
            assetClass="notificationIcon"
            fileType="image/png,image/gif"
            disabled={isLoading}
          />
          <InputBox
            name="Android Firebase Service"
            assetClass="androidFirebaseServiceFile"
            fileType="application/JSON"
            value={assets.androidFirebaseServiceFile}
            url={assetsUrls.androidFirebaseServiceFile}
            onChange={updateAssets('androidFirebaseServiceFile')}
            disabled={isLoading}
          />

          <InputBox
            name="Firebase Service Account Key File"
            assetClass="firebaseServiceAccountKeyFile"
            fileType="application/JSON"
            value={assets.firebaseServiceAccountKeyFile}
            url={assetsUrls.firebaseServiceAccountKeyFile}
            onChange={updateAssets('firebaseServiceAccountKeyFile')}
            disabled={isLoading}
          />

          <InputBox
            name="Android Store File"
            assetClass="androidStoreFile"
            fileType=".keystore,.jks"
            value={assets.androidStoreFile}
            url={assetsUrls.androidStoreFile}
            onChange={updateAssets('androidStoreFile')}
            disabled={isLoading}
          />
          <InputBox
            name="Android PEM File"
            assetClass="androidKeyStorePemFile"
            fileType=".pem"
            url={assetsUrls.androidKeyStorePemFile}
            value={assets.androidKeyStorePemFile}
            onChange={updateAssets('androidKeyStorePemFile')}
            disabled={isLoading}
          />
        </>
      )}

      {/* {!assets.androidStoreFile && (
        <View>
          <ButtonV2
            variant="FILLED"
            color="SECONDARY"
            size="MEDIUM"
            onPress={() => autoGenerateAndroidStoreFile()}
            loading={autoGeneratorLoading}>
            Auto Generate Store File
          </ButtonV2>
        </View>
      )} */}
      <Button
        onPress={() => !isLoading && props.setScreen('SECRETS')}
        style={{bottom: 10, right: 10, alignSelf: 'flex-end', marginTop: 10}}
        text={isLoading ? 'Processing...' : 'Save & Continue'}
        isDisabled={isLoading}
      />
    </ScrollView>
  );
};

type IInputBox = {
  name: string;
  value: string | undefined;
  assetClass:
    | 'splash'
    | 'icon'
    | 'iosFirebaseServiceFile'
    | 'androidFirebaseServiceFile'
    | 'androidStoreFile'
    | 'firebaseServiceAccountKeyFile'
    | 'appStorePrivateKey'
    | 'notificationIcon'
    | 'androidKeyStorePemFile';

  onChange?: (value: string) => void;
  fileType: string;
  url: string | null | undefined;
  width?: number | null | undefined;
  height?: number | null | undefined;
  disabled?: boolean;
};

const InputBox: React.FC<IInputBox> = props => {
  //todo:make this component more generic. right now validation is added for image with width and height
  const param = useParams();
  const dispatch = useDispatch();
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [file, setFile] = React.useState<File | null>(null);
  const [status, setStatus] = React.useState<'input' | 'processing' | 'success' | 'failed'>('input');

  React.useEffect(() => {
    let id: NodeJS.Timeout;
    if (status === 'success')
      id = setTimeout(() => {
        setStatus('input');
      }, 5000);

    return () => {
      if (id) clearTimeout(id);
    };
  }, [status]);

  const handleSubmit = async () => {
    const appID = param.id as string;

    if (!file) return;
    if (isEmpty(appID)) return;

    const response = await UploaderApi.UploadAssetApi({file}, () => {});
    setStatus('processing');

    await BuildManagerApi.postAppAsset(appID, {
      assetClass: props.assetClass,
      uploaderKey: response.data.fileKey,
    });

    props.onChange && props.onChange(response.data.fileKey);

    resetForm();
    setStatus('success');
  };

  const handleDownload = url => {
    if (url) {
      Linking.openURL(url);
    }
  };

  const resetForm = () => {
    setFile(null);
    if (inputRef.current) inputRef.current.value = '';
  };

  const validateImageDimensions = (file, width, height) => {
    if (file.type !== 'image/png' || !width || !height) {
      return Promise.resolve(file);
    }
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        if (img.width !== width || img.height !== height) {
          reject(file);
          dispatch(
            makeToast({
              content: `Image must be of size ${width} x ${height}`,
              appearances: 'error',
              duration: 3000,
            }),
          );
        } else {
          resolve(file);
        }
      };
      img.onerror = () => {
        reject(file);
      };
      img.src = URL.createObjectURL(file);
    });
  };
  return (
    <View style={[styles.inputBoxContainer, props.disabled && styles.disabledContainer]}>
      <Text style={[styles.labelText, props.disabled && styles.disabledLabel]}>{props.name}</Text>
      {props.disabled ? (
        <View style={styles.disabledFileContainer}>
          <Text style={styles.disabledText}>Loading assets...</Text>
        </View>
      ) : !props.value ? (
        <View style={styles.fileInputContainer}>
          <input
            ref={inputRef}
            type="file"
            //todo: make this more generic
            onChange={e => {
              const fileInput = (e.target.files || [])[0];
              validateImageDimensions(fileInput, props.width, props.height)
                ?.then(() => setFile(fileInput))
                ?.catch(() => {
                  resetForm();
                });
            }}
            accept={props.fileType}
          />
          <Pressable
            style={[styles.uploadButton, !file || status === 'processing' ? {backgroundColor: '#bbbbbb'} : {}]}
            disabled={!file || status === 'processing'}
            onPress={handleSubmit}>
            <MaterialCommunityIcons name="cloud-upload" color="white" size={18} />
            {status === 'processing' ? (
              <Text style={styles.uploadText}>Uploading...</Text>
            ) : (
              <Text style={styles.uploadText}>Upload</Text>
            )}
          </Pressable>
        </View>
      ) : (
        <View style={styles.addedFileContainer}>
          <Text>File added</Text>
          <View style={{flexDirection: 'row'}}>
            {!_.isEmpty(props.url) && (
              <Pressable onPress={() => handleDownload(props.url)} style={styles.downloadButton}>
                <MaterialCommunityIcons name="download" size={20} color="#fff" />
              </Pressable>
            )}
            <Pressable onPress={() => props.onChange && props.onChange('')} style={styles.removeButton}>
              <MaterialCommunityIcons name="close" size={20} color="#fff">
                <></>
              </MaterialCommunityIcons>
            </Pressable>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  inputBoxContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  disabledContainer: {
    opacity: 0.6,
  },
  removeButton: {backgroundColor: '#EF4444', paddingVertical: 2.5, marginLeft: 15},
  downloadButton: {backgroundColor: '#0092BC', paddingVertical: 2.5},
  card: {
    position: 'relative',
    backgroundColor: '#f8fafc',
    width: 500,
    padding: 24,
    paddingVertical: 30,
    borderRadius: 10,
    // height: 750,
  },
  labelText: {
    color: '#64748b',
    marginBottom: 10,
    fontWeight: '500',
  },
  disabledLabel: {
    color: '#9ca3af',
  },
  addedFileContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    width: '100%',
  },
  fileInputContainer: {
    paddingTop: 4,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  disabledFileContainer: {
    paddingTop: 4,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    width: '100%',
  },
  disabledText: {
    color: '#9ca3af',
    fontSize: 14,
  },
  uploadButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    display: 'flex',
    flexDirection: 'row',
    backgroundColor: '#0092BC',
    borderRadius: 6,
  },
  uploadText: {
    color: 'white',
    marginLeft: 6,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    marginBottom: 20,
  },
  loadingText: {
    marginTop: 12,
    color: '#64748b',
    fontSize: 14,
  },
});
