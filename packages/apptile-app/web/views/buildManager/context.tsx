import React from 'react';

const defaultRecord: RecordType = {
  platform: 'android',
};

type RecordType = {
  platform: 'android' | 'ios';
};

type FeatureFlag = {
  key: string;
  value: string | object | boolean;
};

type LoadingStates = {
  secrets: boolean;
  settings: boolean;
  assets: boolean;
  buildConfig: boolean;
};

const defaultFeatureFlags: FeatureFlag[] | null = null;

const defaultLoadingStates: LoadingStates = {
  secrets: false,
  settings: false,
  assets: false,
  buildConfig: false,
};

const defaultContext = {
  record: defaultRecord,
  updateRecord: (_key: 'platform', _value: 'android' | 'ios') => {},
  featureFlags: defaultFeatureFlags,
  updateFeatureFlags: (_flags: FeatureFlag[] | null) => {},
  loadingStates: defaultLoadingStates,
  setLoadingState: (_key: keyof LoadingStates, _value: boolean) => {},
};

export type IBuildPlatformContext = typeof defaultContext;

export const BuildPlatformContext = React.createContext<IBuildPlatformContext>(defaultContext);

export const BuildPlatformContextProvider: React.FC = props => {
  const [record, setRecord] = React.useState<RecordType>({
    platform: 'android',
  });

  const [featureFlags, setFeatureFlags] = React.useState<FeatureFlag[] | null>(defaultFeatureFlags);
  const [loadingStates, setLoadingStates] = React.useState<LoadingStates>(defaultLoadingStates);

  const updateRecord = (key: string, value: any) => {
    setRecord(prev => {
      return {...prev, [key]: value};
    });
  };

  const updateFeatureFlags = (flags: FeatureFlag[] | null) => {
    setFeatureFlags(flags);
  };

  const setLoadingState = (key: keyof LoadingStates, value: boolean) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  return (
    <BuildPlatformContext.Provider
      value={{record, updateRecord, featureFlags, updateFeatureFlags, loadingStates, setLoadingState}}>
      {props.children}
    </BuildPlatformContext.Provider>
  );
};

export const useBuildPlatformContext = () => {
  return React.useContext(BuildPlatformContext);
};
