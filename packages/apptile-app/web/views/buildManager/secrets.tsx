import React from 'react';
import {View, Text, StyleSheet, TextInput} from 'react-native';
import {BackButton, Title, Button} from './shared';
import {useParams} from '@/root/web/routing.web';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {isEmpty, keyBy, get, isEqual} from 'lodash';
import validator from 'validator';
import {useBuildPlatformContext} from './context';

type ISecrets = {
  setScreen: (v: 'SETTINGS' | 'ASSETS' | 'SECRETS' | 'BUILD_CONFIG') => void;
};

type ISecretesResponse = {
  id: string;
  appId: string;
  secretClass: string;
  deletedAt: Date | null;
  createdAt: Date;
  secret: string;
};

export const Secrets: React.FC<ISecrets> = props => {
  const {record} = useBuildPlatformContext();
  const [secrets, setSecrets] = React.useState(
    record.platform === 'android'
      ? {
          keyAlias: '',
          storePassword: '',
          keyPassword: '',
        }
      : {teamId: '', appStoreApiKey: '', appStoreIssuerId: ''},
  );
  const [isValid, setValid] = React.useState(true);
  const param = useParams();
  const [stateComp, setStateComp] = React.useState({});

  const updateSecrets = (key: keyof typeof secrets) => {
    return (value: string | string[]) => {
      setSecrets(prev => {
        return {...prev, [key]: value};
      });
    };
  };

  React.useEffect(() => {
    let checkValidity = true;

    Object.values(secrets).forEach(content => {
      if (isEmpty(content) || !validator.isLength(content, {min: 2})) {
        checkValidity = false;
      }
    });

    setValid(checkValidity);
  }, [secrets]);

  React.useEffect(() => {
    const fetchConfig = async () => {
      try {
        const appID = param.id as string;
        if (isEmpty(appID)) return;

        const response = await BuildManagerApi.getActiveAppSecret<ISecretesResponse[]>(appID);
        const mappedResponse = keyBy(response.data, 'secretClass');
        const pickedVal =
          record.platform === 'android'
            ? {
                keyAlias: get(mappedResponse, ['keyAlias', 'secret'], ''),
                storePassword: get(mappedResponse, ['storePassword', 'secret'], ''),
                keyPassword: get(mappedResponse, ['keyPassword', 'secret'], ''),
              }
            : {
                teamId: get(mappedResponse, ['teamId', 'secret'], ''),
                appStoreApiKey: get(mappedResponse, ['appStoreApiKey', 'secret'], ''),
                appStoreIssuerId: get(mappedResponse, ['appStoreIssuerId', 'secret'], ''),
              };

        setSecrets(pickedVal);
        setStateComp(pickedVal);
      } catch (err) {}
    };

    fetchConfig();
  }, [param.id, record.platform]);

  const onSaveSubmitHandler = async () => {
    try {
      const appID = param.id as string;
      // if (isEmpty(appID) || !isValid) return;

      if (!isEqual(secrets, stateComp)) {
        const promiseRequest = Object.entries(secrets).map(async entry => {
          const [secretClass, content] = entry;
          return await BuildManagerApi.postAppSecret(appID, {
            secret: content,
            secretClass: secretClass as any,
          });
        });

        await Promise.all(promiseRequest);
      }
    } catch (err) {}

    props.setScreen('BUILD_CONFIG');
  };

  return (
    <View style={styles.card}>
      <BackButton onPress={() => props.setScreen('ASSETS')} />
      <Title content="Secrets" />
      {record.platform === 'android' ? (
        <>
          <InputBox name="Key Alias" value={secrets.keyAlias} onChange={updateSecrets('keyAlias')} />
          <InputBox name="Store Password" value={secrets.storePassword} onChange={updateSecrets('storePassword')} />
          <InputBox name="Key Password" value={secrets.keyPassword} onChange={updateSecrets('keyPassword')} />
        </>
      ) : (
        <>
          <InputBox name="Team Id" value={secrets.teamId} onChange={updateSecrets('teamId')} />
          <InputBox name="Appstore Api Key" value={secrets.appStoreApiKey} onChange={updateSecrets('appStoreApiKey')} />
          <InputBox
            name="Appstore Issuer Id"
            value={secrets.appStoreIssuerId}
            onChange={updateSecrets('appStoreIssuerId')}
          />
        </>
      )}
      <Button
        onPress={onSaveSubmitHandler}
        text="Save & Continue"

        // isDisabled={!isValid}
      />
    </View>
  );
};

type IInputBox = {
  name: string;
  value: string | undefined;
  onChange?: (value: string) => void;
};

const InputBox: React.FC<IInputBox> = props => {
  return (
    <View style={styles.inputBoxContainer}>
      <Text style={styles.labelText}>{props.name}</Text>
      <TextInput style={styles.inputField} maxLength={65} defaultValue={props.value} onChangeText={props.onChange} />
    </View>
  );
};

const styles = StyleSheet.create({
  inputBoxContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  inputField: {
    outlineStyle: 'none',
    fontSize: 15,
    backgroundColor: '#f1f5f9',
    padding: 10,
    color: '#475569',
    width: '100%',
  },
  card: {
    position: 'relative',
    backgroundColor: '#f8fafc',
    width: 500,
    padding: 24,
    paddingVertical: 30,
    borderRadius: 10,
    // height: '100%',
  },
  labelText: {
    color: '#64748b',
    marginBottom: 10,
  },
});
