import React from 'react';
import {View, Text, StyleSheet, TextInput, ActivityIndicator} from 'react-native';
import {BackButton, Title, Button} from './shared';
import {useParams} from '@/root/web/routing.web';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {isEmpty, keyBy, get, isEqual} from 'lodash';
import validator from 'validator';
import {useBuildPlatformContext} from './context';
import {useDispatch} from 'react-redux';
import {makeToast} from '@/root/web/actions/toastActions';

type ISecrets = {
  setScreen: (v: 'SETTINGS' | 'ASSETS' | 'SECRETS' | 'BUILD_CONFIG') => void;
};

type ISecretesResponse = {
  id: string;
  appId: string;
  secretClass: string;
  deletedAt: Date | null;
  createdAt: Date;
  secret: string;
};

export const Secrets: React.FC<ISecrets> = props => {
  const {record, loadingStates, setLoadingState} = useBuildPlatformContext();
  const dispatch = useDispatch();
  const [secrets, setSecrets] = React.useState(
    record.platform === 'android'
      ? {
          keyAlias: '',
          storePassword: '',
          keyPassword: '',
        }
      : {teamId: '', appStoreApiKey: '', appStoreIssuerId: ''},
  );
  const [isValid, setValid] = React.useState(true);
  const [hasEmptyFields, setHasEmptyFields] = React.useState(false);
  const [validationErrors, setValidationErrors] = React.useState<string[]>([]);
  const param = useParams();
  const [stateComp, setStateComp] = React.useState({});
  const [isInitialLoad, setIsInitialLoad] = React.useState(true);

  const updateSecrets = (key: keyof typeof secrets) => {
    return (value: string | string[]) => {
      setSecrets(prev => {
        return {...prev, [key]: value};
      });
    };
  };

  React.useEffect(() => {
    let checkValidity = true;
    let emptyFieldsCount = 0;
    const errors: string[] = [];

    Object.entries(secrets).forEach(([key, content]) => {
      if (isEmpty(content)) {
        emptyFieldsCount++;
      } else if (!validator.isLength(content, {min: 2})) {
        checkValidity = false;
        errors.push(`${key} must be at least 2 characters long`);
      }
    });

    const allFieldsEmpty = emptyFieldsCount === Object.keys(secrets).length;
    const hasEmptyButNotAll = emptyFieldsCount > 0 && emptyFieldsCount < Object.keys(secrets).length;

    setHasEmptyFields(hasEmptyButNotAll);
    setValidationErrors(errors);

    // Valid if all fields are empty (customer doesn't have details) or all fields are properly filled
    setValid(allFieldsEmpty || (emptyFieldsCount === 0 && checkValidity));
  }, [secrets]);

  React.useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoadingState('secrets', true);
        const appID = param.id as string;
        if (isEmpty(appID)) {
          setLoadingState('secrets', false);
          return;
        }

        const response = await BuildManagerApi.getActiveAppSecret<ISecretesResponse[]>(appID);
        const mappedResponse = keyBy(response.data, 'secretClass');
        const pickedVal =
          record.platform === 'android'
            ? {
                keyAlias: get(mappedResponse, ['keyAlias', 'secret'], ''),
                storePassword: get(mappedResponse, ['storePassword', 'secret'], ''),
                keyPassword: get(mappedResponse, ['keyPassword', 'secret'], ''),
              }
            : {
                teamId: get(mappedResponse, ['teamId', 'secret'], ''),
                appStoreApiKey: get(mappedResponse, ['appStoreApiKey', 'secret'], ''),
                appStoreIssuerId: get(mappedResponse, ['appStoreIssuerId', 'secret'], ''),
              };

        setSecrets(pickedVal);
        setStateComp(pickedVal);
        setIsInitialLoad(false);
      } catch (err) {
        console.error('Error fetching secrets:', err);
        dispatch(
          makeToast({
            content: 'Failed to load secrets configuration. Please try again.',
            appearances: 'error',
            duration: 5000,
          }),
        );
      } finally {
        setLoadingState('secrets', false);
      }
    };

    fetchConfig();
  }, [param.id, record.platform]);

  const onSaveSubmitHandler = async () => {
    try {
      setLoadingState('secrets', true);
      const appID = param.id as string;

      if (isEmpty(appID)) {
        dispatch(
          makeToast({
            content: 'App ID is missing. Cannot save secrets.',
            appearances: 'error',
            duration: 5000,
          }),
        );
        return;
      }

      if (!isValid) {
        dispatch(
          makeToast({
            content: 'Please fix validation errors before continuing.',
            appearances: 'error',
            duration: 5000,
          }),
        );
        return;
      }

      // Check if all fields are empty - if so, skip API call
      const allFieldsEmpty = Object.values(secrets).every(value => isEmpty(value));

      if (allFieldsEmpty) {
        dispatch(
          makeToast({
            content: 'No secrets to save. Proceeding to next step.',
            appearances: 'info',
            duration: 3000,
          }),
        );
        props.setScreen('BUILD_CONFIG');
        return;
      }

      if (!isEqual(secrets, stateComp)) {
        // Only save non-empty fields
        const nonEmptySecrets = Object.entries(secrets).filter(([_, content]) => !isEmpty(content));

        if (nonEmptySecrets.length > 0) {
          const promiseRequest = nonEmptySecrets.map(async entry => {
            const [secretClass, content] = entry;
            return await BuildManagerApi.postAppSecret(appID, {
              secret: content,
              secretClass: secretClass as any,
            });
          });

          await Promise.all(promiseRequest);

          dispatch(
            makeToast({
              content: 'Secrets saved successfully!',
              appearances: 'success',
              duration: 3000,
            }),
          );
        }
      }

      props.setScreen('BUILD_CONFIG');
    } catch (err) {
      console.error('Error saving secrets:', err);
      dispatch(
        makeToast({
          content: 'Failed to save secrets. Please try again.',
          appearances: 'error',
          duration: 5000,
        }),
      );
    } finally {
      setLoadingState('secrets', false);
    }
  };

  const isLoading = loadingStates.secrets;

  return (
    <View style={styles.card}>
      <BackButton onPress={() => props.setScreen('ASSETS')} />
      <Title content="Secrets" />

      {isLoading && isInitialLoad && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0075F2" />
          <Text style={styles.loadingText}>Loading secrets configuration...</Text>
        </View>
      )}

      {!isLoading && (
        <>
          {/* Validation Feedback */}
          {hasEmptyFields && (
            <View style={styles.warningContainer}>
              <Text style={styles.warningTitle}>⚠️ Incomplete Configuration</Text>
              <Text style={styles.warningText}>
                Some fields are empty. Either fill all fields or leave all fields empty if you don't have these details
                yet.
              </Text>
            </View>
          )}

          {validationErrors.length > 0 && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorTitle}>❌ Validation Errors</Text>
              {validationErrors.map((error, index) => (
                <Text key={index} style={styles.errorText}>
                  • {error}
                </Text>
              ))}
            </View>
          )}

          {Object.values(secrets).every(value => isEmpty(value)) && !isInitialLoad && (
            <View style={styles.infoContainer}>
              <Text style={styles.infoTitle}>ℹ️ No Secrets Configured</Text>
              <Text style={styles.infoText}>
                No secrets are currently configured. You can proceed to the next step or add your {record.platform}{' '}
                secrets now.
              </Text>
            </View>
          )}
        </>
      )}

      {/* Input Fields */}
      {record.platform === 'android' ? (
        <>
          <InputBox
            name="Key Alias"
            value={secrets.keyAlias}
            onChange={updateSecrets('keyAlias')}
            disabled={isLoading}
          />
          <InputBox
            name="Store Password"
            value={secrets.storePassword}
            onChange={updateSecrets('storePassword')}
            disabled={isLoading}
          />
          <InputBox
            name="Key Password"
            value={secrets.keyPassword}
            onChange={updateSecrets('keyPassword')}
            disabled={isLoading}
          />
        </>
      ) : (
        <>
          <InputBox name="Team Id" value={secrets.teamId} onChange={updateSecrets('teamId')} disabled={isLoading} />
          <InputBox
            name="Appstore Api Key"
            value={secrets.appStoreApiKey}
            onChange={updateSecrets('appStoreApiKey')}
            disabled={isLoading}
          />
          <InputBox
            name="Appstore Issuer Id"
            value={secrets.appStoreIssuerId}
            onChange={updateSecrets('appStoreIssuerId')}
            disabled={isLoading}
          />
        </>
      )}

      <Button
        onPress={onSaveSubmitHandler}
        text={isLoading ? 'Saving...' : 'Save & Continue'}
        isDisabled={isLoading || !isValid}
      />
    </View>
  );
};

type IInputBox = {
  name: string;
  value: string | undefined;
  onChange?: (value: string) => void;
  disabled?: boolean;
};

const InputBox: React.FC<IInputBox> = props => {
  return (
    <View style={styles.inputBoxContainer}>
      <Text style={[styles.labelText, props.disabled && styles.disabledLabel]}>{props.name}</Text>
      <TextInput
        style={[styles.inputField, props.disabled && styles.disabledInput]}
        maxLength={65}
        defaultValue={props.value}
        onChangeText={props.onChange}
        editable={!props.disabled}
        placeholder={props.disabled ? 'Loading...' : `Enter ${props.name.toLowerCase()}`}
        placeholderTextColor={props.disabled ? '#9ca3af' : '#a0a0a0'}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  inputBoxContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  inputField: {
    outlineStyle: 'none',
    fontSize: 15,
    backgroundColor: '#f1f5f9',
    padding: 10,
    color: '#475569',
    width: '100%',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  disabledInput: {
    backgroundColor: '#f8fafc',
    color: '#9ca3af',
    borderColor: '#e5e7eb',
  },
  card: {
    position: 'relative',
    backgroundColor: '#f8fafc',
    width: 500,
    padding: 24,
    paddingVertical: 30,
    borderRadius: 10,
  },
  labelText: {
    color: '#64748b',
    marginBottom: 10,
    fontWeight: '500',
  },
  disabledLabel: {
    color: '#9ca3af',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    marginBottom: 20,
  },
  loadingText: {
    marginTop: 12,
    color: '#64748b',
    fontSize: 14,
  },
  warningContainer: {
    backgroundColor: '#fef3c7',
    borderLeftWidth: 4,
    borderLeftColor: '#f59e0b',
    padding: 12,
    marginBottom: 16,
    borderRadius: 4,
  },
  warningTitle: {
    color: '#92400e',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  warningText: {
    color: '#92400e',
    fontSize: 13,
    lineHeight: 18,
  },
  errorContainer: {
    backgroundColor: '#fee2e2',
    borderLeftWidth: 4,
    borderLeftColor: '#dc2626',
    padding: 12,
    marginBottom: 16,
    borderRadius: 4,
  },
  errorTitle: {
    color: '#dc2626',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  errorText: {
    color: '#7f1d1d',
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 2,
  },
  infoContainer: {
    backgroundColor: '#dbeafe',
    borderLeftWidth: 4,
    borderLeftColor: '#3b82f6',
    padding: 12,
    marginBottom: 16,
    borderRadius: 4,
  },
  infoTitle: {
    color: '#1d4ed8',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  infoText: {
    color: '#1e40af',
    fontSize: 13,
    lineHeight: 18,
  },
});
