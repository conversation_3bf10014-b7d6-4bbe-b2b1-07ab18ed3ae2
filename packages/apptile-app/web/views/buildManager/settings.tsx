import React, {useState} from 'react';
import {View, Text, StyleSheet, TextInput, TouchableOpacity} from 'react-native';
import {isEmpty} from 'lodash';
import validator from 'validator';
import {useSelector, useDispatch} from 'react-redux';
import {EditorRootState} from '../../../store/EditorRootState';
import {useParams} from '@/root/web/routing.web';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {Title, Button, BackButton} from './shared';
import {pick, isEqual} from 'lodash';
import {useNavigate} from 'react-router';
import {MaterialCommunityIcons} from 'apptile-core';
import Modal from '@/root/web/components-v2/base/Modal';
import {makeToast} from '@/root/web/actions/toastActions';

import Banner from './Banner';

type ISettings = {
  setScreen: (v: 'SETTINGS' | 'ASSETS' | 'SECRETS' | 'BUILD_CONFIG') => void;
};

type ISettingsData = {
  bundleUrlScheme: string;
  displayName: string;
  androidBundleIdentifier: string;
  iosBundleIdentifier: string;
  appHost: string;
  appHost2: string;
  nextUpdateCheck: string | null;
};

type ISettingsResponse = {
  id: string;
  appId: string;
  bundleUrlScheme: string;
  displayName: string;
  androidBundleIdentifier: string;
  iosBundleIdentifier: string;
  appHost: string;
  appHost2: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  nextUpdateCheck: string | null;
};

export const Settings: React.FC<ISettings> = props => {
  const param = useParams();
  const navigate = useNavigate();

  const [settings, setSettings] = React.useState<ISettingsData>({
    bundleUrlScheme: '',
    displayName: '',
    androidBundleIdentifier: '',
    iosBundleIdentifier: '',
    appHost: '',
    appHost2: '',
    nextUpdateCheck: null,
  });

  const [originalSettings, setOriginalSettings] = React.useState<ISettingsData>({
    bundleUrlScheme: '',
    displayName: '',
    androidBundleIdentifier: '',
    iosBundleIdentifier: '',
    appHost: '',
    appHost2: '',
    nextUpdateCheck: null,
  });

  const [recordExist, setRecordExist] = React.useState(false);
  const [isValid, setValid] = React.useState(true);

  React.useEffect(() => {
    validateSettings(settings, setValid);
  }, [settings]);

  const updateSettings = (key: keyof typeof settings) => {
    return (value: string | string[]) => {
      setSettings(prev => {
        return {...prev, [key]: value};
      });
    };
  };

  React.useEffect(() => {
    const fetchConfig = async () => {
      try {
        const appID = param.id as string;
        if (isEmpty(appID)) return;

        const response = await BuildManagerApi.getAppSettings<ISettingsResponse>(appID);
        const pickedVal = pick(response.data, [
          'bundleUrlScheme',
          'displayName',
          'appHost',
          'appHost2',
          'androidBundleIdentifier',
          'iosBundleIdentifier',
          'nextUpdateCheck'
        ]);
        setSettings(pickedVal);
        setOriginalSettings(pickedVal);
        setRecordExist(true);
      } catch (err) {}
    };

    fetchConfig();
  }, [param.id]);

  const onSaveSubmitHandler = async () => {
    try {
      const appID = param.id as string;
      if (isEmpty(appID) || !isValid) return;

      // Compute only the changed values
      const changedSettings = Object.keys(settings).reduce((diff, key) => {
        if (settings[key] !== originalSettings[key]) {
          diff[key] = settings[key];
        }
        return diff;
      }, {} as Partial<ISettings>);

      // Proceed only if there are changes
      if (Object.keys(changedSettings).length !== 0) {
        if (recordExist) {
          // update
          await BuildManagerApi.patchAppSettings<ISettingsResponse>(appID, changedSettings);
        } else {
          // create
          await BuildManagerApi.postAppSettings<ISettingsResponse>(appID, settings);
        }
      }
    } catch (err) {
      console.error(err); // Log the error for debugging
    }

    props.setScreen('ASSETS');
  };

  return (
    <View>
      <ResetCredentialsModal />
      <View style={styles.card}>
        <View style={{justifyContent: 'space-between', flexDirection: 'row'}}>
          <TouchableOpacity style={styles.buildListButton} onPress={() => navigate(`../builds/list`)}>
            <MaterialCommunityIcons name="format-list-bulleted-type" color="#0075F2" size={18} />
          </TouchableOpacity>
        </View>
        <Title content="Settings" />
        <InputBox name="Display Name" value={settings.displayName} onChange={updateSettings('displayName')} />
        <InputBox
          name="Url Scheme"
          value={settings.bundleUrlScheme}
          onChange={updateSettings('bundleUrlScheme')}
          info="Used to identify the app in deeplinks. eg. apptileprvw"
        />
        <InputBox
          name="Android Bundle Identifier"
          value={settings.androidBundleIdentifier}
          onChange={updateSettings('androidBundleIdentifier')}
          info="Used to identify the app in app store. eg. com.apptile.apptileprvw"
        />
        <InputBox
          name="IOS Bundle Identifier"
          value={settings.iosBundleIdentifier}
          onChange={updateSettings('iosBundleIdentifier')}
          info="Used to identify the app in app store. eg. com.apptile.apptileprvw"
        />
        <InputBox
          name="App Host"
          value={settings.appHost}
          onChange={updateSettings('appHost')}
          info="Used to associate the app with website. eg. apptile.com"
        />
        <InputBox
          name="Multi Match App Host"
          value={settings.appHost2}
          onChange={updateSettings('appHost2')}
          info="Used to match the app with multiple sub domains. eg. *.apptile.com"
        />
        <InputBox
          name="Next Update Check"
          value={settings.nextUpdateCheck!}
          onChange={updateSettings('nextUpdateCheck')}
          info="[DEV]: Time used to check for next appConfig update. [value in hours]. eg. 24"
        />
        <Button onPress={onSaveSubmitHandler} text="Save & Continue" isDisabled={!isValid} />
      </View>
    </View>
  );
};

const ResetCredentialsModal = () => {
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const dispatch = useDispatch();
  const {user} = useSelector((state: EditorRootState) => state.user);
  const triggeredBy = `${user.firstname} ${user.lastname}`;
  const [isModalVisible, setModalVisible] = useState(false);
  const [nukeCode, setNukeCode] = useState('');

  const handleReset = async (appId: string, nukeCode: string, resettedBy: string, platform: string) => {
    try {
      await BuildManagerApi.resetCredentials(appId, nukeCode, platform, resettedBy);
      setModalVisible(false);

      dispatch(
        makeToast({
          content: 'Credentials Cleared Successfully!',
          appearances: 'success',
          duration: 5000,
        }),
      );
    } catch (err) {
      dispatch(
        makeToast({
          content: 'You are Unauthorized!! Try One more time and it will self destruct your workstation!',
          appearances: 'error',
          duration: 5000,
        }),
      );
      setModalVisible(false);
    }
  };

  return (
    <View>
      <Button onPress={() => setModalVisible(true)} text="Reset Credentials" />
      <Modal
        onVisibleChange={setModalVisible}
        visible={isModalVisible}
        content={
          <View style={{padding: 20, alignItems: 'center'}}>
            <Text style={{fontSize: 16, marginBottom: 10}}>Are you sure you want to reset the build details?</Text>
            <TextInput
              placeholder="Enter Nuke Code"
              secureTextEntry
              value={nukeCode}
              onChangeText={setNukeCode}
              style={{
                width: '100%',
                padding: 10,
                borderWidth: 1,
                borderRadius: 5,
                marginBottom: 10,
              }}
            />
            <Button onPress={() => handleReset(appId, nukeCode, triggeredBy, 'ios')} text="Reset IOS Credentials" />
            <Button
              onPress={() => handleReset(appId, nukeCode, triggeredBy, 'android')}
              text="Reset Android Credentials"
            />
            <Button onPress={() => setModalVisible(false)} text="Close" />
          </View>
        }
      />
    </View>
  );
};

type IInputBox = {
  name: string;
  value: string;
  onChange?: (value: string) => void;
  info?: string;
};

const InputBox: React.FC<IInputBox> = props => {
  return (
    <View style={styles.inputBoxContainer}>
      <Text style={styles.labelText}>{props.name}</Text>
      <TextInput
        style={styles.inputField}
        maxLength={65}
        defaultValue={props.value}
        onChangeText={props.onChange}
        autoCompleteType="off"
      />
      {props.info && <Text style={{fontSize: 11, color: 'grey'}}>{props.info}</Text>}
    </View>
  );
};

const validateSettings = (record: ISettingsData, valCB: (val: boolean) => void) => {
  let isValid = true;

  for (const key in validationMap) {
    const validatorFunc = validationMap[key];
    if (!validatorFunc(record[key], record)) {
      isValid = false;
    }
  }

  valCB(isValid);
};

export const validationMap: Partial<Record<keyof ISettingsData, (...args: any[]) => boolean>> = {
  bundleUrlScheme: (val: string) => {
    if (isEmpty(val)) {
      return false;
    }

    return validator.isLength(val, {min: 2});
  },
  displayName: (val: string) => {
    if (isEmpty(val)) {
      return false;
    }

    return validator.isLength(val, {min: 2});
  },

  androidBundleIdentifier: (val: string) => {
    if (isEmpty(val)) {
      return false;
    }

    return validator.isLength(val, {min: 2});
  },
  iosBundleIdentifier: (val: string) => {
    if (isEmpty(val)) {
      return false;
    }

    return validator.isLength(val, {min: 2});
  },

  appHost: (val: string) => {
    if (isEmpty(val)) {
      return false;
    }

    return validator.isLength(val, {min: 2});
  },
};

const styles = StyleSheet.create({
  inputBoxContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  inputField: {
    outlineStyle: 'none',
    fontSize: 15,
    backgroundColor: '#f1f5f9',
    padding: 10,
    color: '#475569',
    width: '100%',
  },
  buildListButton: {
    marginBottom: 20,
    width: '100%',
    alignItems: 'flex-end',
  },
  card: {
    position: 'relative',
    backgroundColor: '#f8fafc',
    width: 500,
    padding: 24,
    paddingVertical: 30,
    borderRadius: 10,
    height: '100%',
  },
  labelText: {
    color: '#64748b',
    marginBottom: 10,
  },
});
