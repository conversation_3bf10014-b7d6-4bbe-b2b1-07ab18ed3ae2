import React, {Ref, useCallback, useEffect, useRef, useState} from 'react';
import {
  StyleSheet,
  View,
  Text,
  LayoutChangeEvent,
  TouchableOpacity,
  Image,
  Dimensions,
  ScaledSize,
  TextInput,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {useDispatch, useSelector} from 'react-redux';

import ApptileApp from '../../../../app/ApptileApp';
import {ThemeContainer} from 'apptile-core';
import NavigationEditorContainer from '../../navigation/containers/NavigationEditorContainer';
import ModelBrowserContainer from '../../modelBrowser/containers/ModelBrowserContainer';
import StyleModelBrowserContainer from '../../modelBrowser/containers/StyleModelBrowserContainer';
import PagesListControl from '../../pagesList/components/PagesListControl';
import PluginListControlContainer from '../../pluginList/containers/PluginListControlContainer';
import EditorRightPaneTabsContainer from '../containers/EditorRightPaneTabsContainer';
import {useParams} from '../../../routing.web';
import {HotKeys} from 'react-hotkeys';
import {HiddenPasteInput} from '../../../components/HiddenPasteInput';
import {HOTKEY_MAPS} from '../../../common/hotKeyMaps';
import AuthTopBanner from '../../auth/components/AuthTopBanner';
import {useNavigate} from 'react-router';
import phoneBG from '../../../assets/images/phone-frame.png';
import {PlatformState} from '@/root/web/store/PlatformReducer';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';
import {useCallbackRef} from 'apptile-core';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withSpring, withTiming} from 'react-native-reanimated';
import ShopifyLeftPane from '../../../integrations/shopify/views/ShopifyLeftPane';
import ShopifyRightPane from '../../../integrations/shopify/views/ShopifyRightPane';
import PreviewButton from './PreviewButton';
import PublishHistory from './PublishHistory';
import PublishButton from './PublishButton';
import {ApptileCanvasScaleContext} from 'apptile-core';
import ImageEditorContainer from '../../imageEditor/containers/ImageEditorContainer';
import LinkingEditor from '../../linkingEditor/LinkingEditor';
import GlobalAppSettingsEditor from '../../globalAppSettings/GlobalAppSettingsEditor';
import Button from '@/root/web/components-v2/base/Button';
import BlueprintsDialog from '../../blueprintsExport/blueprintsSaveDialog';
import {selectAppConfig} from 'apptile-core';
import {selectAppModel} from 'apptile-core';
import {useIsEditable} from 'apptile-core';
import {initApptileIsEditable} from 'apptile-core';
import {CustomDragLayer} from '../../../components/CustomDragLayer';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import QRCode from 'react-qr-code';
import CollapsiblePanel from '@/root/web/components/CollapsiblePanel';
import theme from '@/root/web/styles-v2/theme';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import generatePreviewURL from '@/root/web/common/generatePreviewURL';
import EditorContext from '@/root/web/context/editorContext';
import {fillMandtoryFields} from '@/root/web/actions/onboardingActions';
import MandatoryFields from './MandatoryFields';
import {deleteBindingError, toggleBindingErrors, openPropertyInspector} from '@/root/web/actions/editorActions';
import {getNavigationContext} from 'apptile-core';
import {selectPlugin} from 'apptile-core';
import {Resizable} from 're-resizable';
import BetaBuildTag from '@/root/web/components/BetaBuildTag';
import {selectAppsById} from '@/root/web/selectors/AppSelectors';

interface EditorMainProps {
  editor: any; //EditorState
  platform: PlatformState;
  changeAppConfig: (appId: string, orgId: string, forkId: string | number, branchName?: string) => void;
  orgs: any;
  fetchOrgs: () => void;
  saveAppState: (newSave: boolean, showToast: boolean, message?: string) => void;
  updateApp: (publishedAppSaveId: number) => void;
  editorCopyAction: () => void;
  editorPasteAction: (pasteString: string) => void;
  configureDatasources: (appId: string, forceUpdateSecrets: boolean) => void;
  clearDatasourcesCredentials: () => void;
  softRestartConfig: () => void;
  changeAppContextData: (appId: string) => void;
  initApptileTilesMode: (isTilesOnly: boolean) => void;
  params: Record<string, string>;
}

interface EditorState {
  isEmbeddedInShopify: boolean;
  propertyPanelOpen: boolean;
  isEditable: boolean;
  viewHeight: number;
  viewWidth: number;
  canvasScale: number;
}

type EditorProps = EditorMainProps;

function recurse(object: any, binding: string, path: string[]): undefined | string[] {
  if (object === binding) {
    return path;
  } else if (Array.isArray(object)) {
    for (let index = 0; index < object.length; ++index) {
      const result = recurse(object[index], binding, path.concat(index.toString()));
      if (result) {
        return result;
      }
    }
  } else if (object && typeof object === 'object') {
    for (let key in object) {
      const result = recurse(object[key], binding, path.concat(key));
      if (result) {
        return result;
      }
    }
  }
}

const BuilderEditor: React.FC<EditorProps> = props => {
  const {
    platform,
    changeAppConfig,
    fetchOrgs,
    saveAppState,
    editorCopyAction,
    editorPasteAction,
    params,
    configureDatasources,
    clearDatasourcesCredentials,
    softRestartConfig,
    changeAppContextData,
    initApptileTilesMode,
  } = props;
  const {orgs, updateApp} = props;
  const dispatch = useDispatch();

  const pasteInputNode: Ref<any> = useRef();

  const [isEmbeddedInShopify] = useState(platform?.isEmbeddedInShopify);
  const [propertyPanelOpen] = useState(true);
  const isEditable = useIsEditable();
  const toggleIsEditable = () => dispatch(initApptileIsEditable(!isEditable));
  const toggleErrorView = () => {
    dispatch(toggleBindingErrors());
  };
  const [viewHeight, setViewHeight] = useState(Dimensions.get('window').height);
  const [viewWidth, setViewWidth] = useState(Dimensions.get('window').width);
  const [canvasScale, setCanvasScale] = useState(0.75);
  const av_scale = useSharedValue(0.75);
  const styleAnimatedScale = useAnimatedStyle(() => {
    return {
      transform: [
        {
          scale: withSpring(av_scale.value, {restDisplacementThreshold: 0.001, restSpeedThreshold: 0.01}),
        },
      ],
    };
  }, [av_scale]);
  const navigate = useNavigate();
  const [isExportDialogOpen, setOpenDialog] = useState(false);
  const appConfig = useSelector(selectAppConfig);
  const pageKeysToId = useSelector(state => state.appModel.get('pageKeysToId'));
  const showBindingErrors = useSelector(state => state.editor.showBindingErrors);
  const bindingErrors = useSelector(state => state.editor.bindingErrors);

  const windowListener = useCallback(({window, screen}: {window: ScaledSize; screen: ScaledSize}) => {
    setViewWidth(window.width);
    setViewHeight(window.height);
  }, []);

  useMountEffect(() => {
    const appId = params?.id ?? 1;
    const orgId = params?.orgId;
    const forkId = params?.forkId;
    const branchName = params?.branchName;
    changeAppConfig(appId, orgId, forkId, branchName);
    fetchOrgs();
    Dimensions.addEventListener('change', windowListener);
    dispatch(initApptileIsEditable(false));
  });
  useEffect(() => {
    av_scale.value = canvasScale;
  }, [av_scale, canvasScale]);
  useEffect(() => {
    initApptileTilesMode(false);
  }, []);

  const appId = params?.id;
  useEffect(() => {
    if (appId) {
      changeAppContextData(appId);
    }
  }, [appId, changeAppContextData]);

  const apptileState = useSelector(state => state.apptile);
  const [previewURL, setPreviewURL] = useState<string>();
  useEffect(() => {
    const appId = apptileState?.appId as string;
    const orgId = apptileState?.orgId as string;
    const forkId = apptileState?.forkId;
    const branchName = apptileState?.appBranch;
    const appName = orgs?.appsById[params.id]?.name;
    const orgName = orgs?.orgsById[params.orgId]?.name;
    setPreviewURL(generatePreviewURL(orgId, appId, forkId, branchName, appName, orgName));
  }, [apptileState, orgs, params.id, params.orgId]);

  const getHotkeyHandlers = useCallbackRef(() => {
    return {
      EDITOR_PASTE: () => {
        logger.info('Pasting');
        pasteInputNode?.current.focus();
      },
      EDITOR_COPY: editorCopyAction,
      PUBLISH: () => {
        let message = window && window.prompt('Please enter publish message', 'Updated template');
        if (message) saveAppState(true, true, message || 'Updated template');
      },
      SAVE: () => {
        console.log("Page has been saved using 's' key");
        saveAppState(false, true);
      },
      SOFT_REFRESH: () => {
        console.log("Soft refresh has been triggered using 'r' key");
        softRestartConfig();
      },
      TOGGLE_EDIT: () => {
        console.log("Toggle edit mode has been triggered using 'e' key");
        toggleIsEditable();
      },
    };
  });

  const onLayout = useCallback(
    (e: LayoutChangeEvent) => {
      // setViewWidth(e.nativeEvent.layout.width);
      // setViewHeight(e.nativeEvent.layout.height);
      // const sc = (e.nativeEvent.layout.height * 0.9) / 946;
      const sc = (viewHeight * 0.82) / 946;
      setCanvasScale(sc);
      logger.info('Canvas Scale: ', sc);
    },
    [viewHeight],
  );

  const onRefreshDatasourceCredentials = () => {
    const appId = params?.id ?? null;
    if (appId) {
      configureDatasources(appId, true);
    }
  };

  const onClearDsCredentials = () => {
    clearDatasourcesCredentials();
  };

  const onSoftRefreshEditor = () => {
    const appId = params?.id ?? null;
    if (appId) {
      softRestartConfig(appId, true);
    }
  };

  const onCloseDialog = useCallback(() => {
    setOpenDialog(false);
  }, []);

  const onResolve = useCallback(
    (binding: string) => {
      dispatch(deleteBindingError(binding));
    },
    [dispatch],
  );

  const onLocate = useCallback(
    (binding: string) => {
      console.log('Finding: ' + binding);
      let result: any;
      parentLoop: for (let [pageKey, page] of appConfig.get('pages').entries()) {
        const plugins = page.get('plugins');
        for (let [pluginKey, plugin] of plugins.entries()) {
          const path = recurse(plugin.toJS(), binding, []);
          if (path) {
            result = [pageKey, pluginKey, path];
            break parentLoop;
          }
        }
      }
      console.log('locate finished');
      if (!result) {
        alert('oops! unable to locate this binding');
      } else {
        const context = getNavigationContext();
        for (let key of pageKeysToId.keys()) {
          if (pageKeysToId.get(key) === result[0]) {
            const finalPath = [key, 'plugins', result[1]];
            dispatch(selectPlugin(finalPath));
            dispatch(openPropertyInspector());
            context.navigate(result[0]);
            return;
          }
        }

        logger.info('Attempting to naviate for ', result);
        context.navigate(result[0]);
        const dialog = document.createElement('dialog');
        dialog.innerHTML = `
        <div>
          <div style="max-width: 500px">
            Navigating to the page where this error might be located. Please wait and try again once this dialog closes.
          </div>
        </div>`;
        document.body.appendChild(dialog);
        dialog.showModal();
        setTimeout(() => {
          dialog.close();
          document.body.removeChild(dialog);
        }, 2000);
      }
    },
    [dispatch, appConfig, pageKeysToId],
  );

  const [bindingToSearch, setBindingToSearch] = useState('');

  let errorList = null;
  if (showBindingErrors) {
    errorList = [];
    let bindingErrorEntries: Array<[string, {error: string}]> = Array.from(bindingErrors.entries());
    bindingErrorEntries.sort((a, b) => {
      if (b[1].error.startsWith('TypeError')) {
        return -1;
      } else {
        return 1;
      }
    });

    errorList.push(
      <View>
        <Text>Search a binding</Text>
        <input type="text" value={bindingToSearch} onChange={ev => setBindingToSearch(ev.target.value)} />
        <Button onPress={() => onLocate(bindingToSearch)}>Find</Button>
      </View>,
    );

    for (let [binding, error] of bindingErrorEntries) {
      errorList.push(
        <View
          key={binding}
          style={{
            paddingTop: '10px',
            paddingBottom: '10px',
            paddingLeft: '2px',
            paddingRight: '2px',
            borderStyle: 'solid',
            borderWidth: '1px',
            marginTop: '2px',
            marginBottom: '2px',
            borderRadius: '6px',
          }}>
          <View style={{display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', marginBottom: '10px'}}>
            <Button onPress={() => onLocate(binding)}>Find</Button>
            <Button onPress={() => onResolve(binding)}>Dismiss</Button>
          </View>
          <Text>{binding}</Text>
          <Text style={{color: 'red', fontFamily: 'monospace', fontSize: 10, overflow: 'scroll', textWrap: 'nowrap'}}>
            {error.error}
          </Text>
          <Text
            style={{
              fontSize: 10,
              marginTop: '9px',
              color: '#ccc',
              fontFamily: 'monospace',
              overflow: 'scroll',
              textWrap: 'nowrap',
            }}>
            {error.transpiledFn}
          </Text>
        </View>,
      );
    }

    if (errorList.length == 0) {
      errorList.push(
        <View>
          <Text>No errors so far. Use the navigation tab, generate cache or reload to look for errors.</Text>
        </View>,
      );
    }
  }

  global.BUILDER_EDITOR = true;

  const appsById = useSelector(selectAppsById);
  return (
    <EditorContext.Provider value={{layout: 'editorV1'}}>
      <HotKeys component={'div'} keyMap={HOTKEY_MAPS.EDITOR_GLOBAL} handlers={getHotkeyHandlers()}>
        <ThemeContainer>
          <View style={[styles.editorLayout, {height: viewHeight, width: viewWidth}]} onLayout={onLayout}>
            <CustomDragLayer />
            <HiddenPasteInput
              ref={pasteInputNode}
              onPaste={(event: any) => {
                const pasteString = event.clipboardData.getData('text');
                editorPasteAction(pasteString);
              }}
            />
            {!isEmbeddedInShopify && (
              <Resizable
                defaultSize={{height: '100vh', width: 280}}
                minWidth={280}
                maxWidth="50vw"
                enable={{
                  top: false,
                  right: true,
                  bottom: false,
                  left: false,
                  topRight: false,
                  bottomRight: false,
                  bottomLeft: false,
                  topLeft: false,
                }}
                // style={{ overflow: 'scroll' }}
              >
                <View style={styles.leftPanel}>
                  <AuthTopBanner style={{height: 44}} />
                  <Text
                    style={[
                      commonStyles.baseText,
                      {textAlign: 'center', borderTopColor: '#e5e7eb', borderTopWidth: 1, paddingTop: 8},
                    ]}>
                    App name : {appsById[params?.id]?.name ?? 'App name'}
                  </Text>
                  <ScrollView style={{flex: 1}}>
                    <View style={[styles.leftScrollContainer]}>
                      <View style={{height: 'auto', flex: 1}}>
                        <PagesListControl />
                        <NavigationEditorContainer />
                        <LinkingEditor />
                        {/* <GlobalAppSettingsEditor /> */}
                        <ModelBrowserContainer />
                        <PluginListControlContainer />
                        {/* <ImageEditorContainer /> */}
                      </View>
                    </View>
                  </ScrollView>
                </View>
              </Resizable>
            )}
            {isEmbeddedInShopify && <ShopifyLeftPane />}
            {/* <ShopifyLeftPane /> */}
            <div
              id="appPreviewContainer"
              style={{
                display: showBindingErrors ? 'grid' : 'static',
                gridTemplateColumns: showBindingErrors ? '350px 1fr' : '1fr',
                flex: 1,
              }}>
              <View style={{maxHeight: '100%', overflow: 'scroll'}}>{errorList}</View>
              <View
                style={{
                  height: 45,
                  width: '100%',
                  left: 0,
                  backgroundColor: '#fff',
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  borderBottomColor: '#e5e7eb',
                  borderBottomWidth: 1,
                }}>
                <View style={styles.leftPanelTop}>
                  <View style={styles.rowContainer}>
                    <Button color="CTA" size="EXTRA-SMALL" onPress={() => saveAppState(false, true)}>
                      Save App
                    </Button>
                    <Button
                      variant="PILL"
                      color="PRIMARY"
                      size="EXTRA-SMALL"
                      onPress={() =>
                        window.open(
                          `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/dashboard/store`,
                          '_self',
                        )
                      }>
                      Shopify
                    </Button>
                    <PopoverComponent
                      trigger={
                        <View style={[styles.navButton]}>
                          <Text style={{color: 'white', textAlign: 'center', fontSize: 12}}>Preview App</Text>
                        </View>
                      }>
                      <View style={styles.QRPopover}>
                        {previewURL && (
                          <QRCode size={220} style={QRCodeStyle} value={previewURL} viewBox={`0 0 220 220`} />
                        )}
                        <Text style={[commonStyles.baseText, {textAlign: 'center'}]}>Scan to view on phone</Text>
                      </View>
                    </PopoverComponent>
                    <View style={[styles.rowContainer, {justifyContent: 'flex-end'}]}>
                      <Button
                        containerStyles={{margin: 4}}
                        variant="PILL"
                        color="PRIMARY"
                        size="SMALL"
                        icon="reload"
                        onPress={() => onSoftRefreshEditor()}
                      />
                      <Button
                        containerStyles={{margin: 4}}
                        variant={isEditable ? 'FILLED-PILL' : 'PILL'}
                        color="PRIMARY"
                        icon={isEditable ? 'pencil-off' : 'pencil'}
                        size="SMALL"
                        onPress={() => toggleIsEditable()}
                      />
                    </View>
                  </View>
                </View>
              </View>
              <View style={{flex: 1, flexGrow: 1, flexBasis: 'auto', justifyContent: 'center'}}>
                <Animated.View style={[styles.appContainer, styleAnimatedScale]}>
                  <ApptileCanvasScaleContext.Provider value={canvasScale}>
                    <Image source={phoneBG} style={[StyleSheet.absoluteFill, styles.deviceBezel]} />
                    <View style={styles.appCanvas}>
                      <ApptileApp />
                    </View>
                  </ApptileCanvasScaleContext.Provider>
                </Animated.View>
              </View>
            </div>
            {!isEmbeddedInShopify && (
              <Resizable
                defaultSize={{height: '100vh', width: 375}}
                minWidth={375}
                maxWidth="50vw"
                enable={{
                  top: false,
                  right: false,
                  bottom: false,
                  left: true,
                  topRight: false,
                  bottomRight: false,
                  bottomLeft: false,
                  topLeft: false,
                }}
                // style={{ overflow: 'scroll' }}
              >
                <View style={styles.rightPanel}>
                  <EditorRightPaneTabsContainer />
                </View>
              </Resizable>
            )}
            {isEmbeddedInShopify && <ShopifyRightPane />}
            {/* <ShopifyRightPane /> */}
          </View>
        </ThemeContainer>
      </HotKeys>
      <BetaBuildTag />
    </EditorContext.Provider>
  );
};

const styles = StyleSheet.create({
  editorLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'stretch',
    alignContent: 'stretch',
    overflow: 'hidden',
    backgroundColor: '#f3f4f6',
  },
  deviceBezel: {
    backgroundColor: 'transparent',
    shadowColor: 'black',
    shadowOffset: {width: 10, height: 10},
    shadowRadius: 50,
    shadowOpacity: 0.4,
    overflow: 'visible',
  },
  leftPanel: {
    flex: 1,
    // maxWidth: 280,
    minWidth: 280,
    flexDirection: 'column',
    alignItems: 'stretch',
    alignContent: 'stretch',
    flexGrow: 1,
    height: '100%',
    backgroundColor: '#fff',
    borderRightColor: '#e5e7eb',
    borderRightWidth: 1,
  },
  leftPanelTop: {
    height: '100%',
    flex: 1,
    flexBasis: 'auto',
    justifyContent: 'space-between',
  },
  leftPanelAdj: {
    height: 'auto',
    flex: 1,
    flexBasis: 'auto',
    flexGrow: 0,
    position: 'absolute',
    top: 20,
    right: -48,
    width: 48,
    maxWidth: 48,
  },
  rowContainer: {
    flexDirection: 'row',
    height: '100%',
    flex: 1,
    alignItems: 'center',
    gap: 4,
  },
  leftScrollContainer: {
    flex: 1,
    padding: 2,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    fontSize: 12,
    flexBasis: 'auto',
  },
  appContainer: {
    width: 445,
    height: 888,
    flexGrow: 0,
    flexShrink: 0,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    alignSelf: 'center',
    overflow: 'visible',
    backgroundColor: 'rgba(0,0,0,0)',
    transformOrigin: 'center 25%',
  },
  appCanvas: {
    top: 23,
    left: 25,
    width: 390,
    height: 844,
    position: 'absolute',
    borderRadius: 45,
    overflow: 'hidden',
    flexGrow: 0,
    flexShrink: 0,
  },
  rightPanel: {
    flex: 1,
    minWidth: 375,
    height: '100%',
    flexDirection: 'column',
    backgroundColor: '#fff',
    borderLeftColor: '#e5e7eb',
    borderLeftWidth: 1,
  },
  ml4: {marginLeft: 4},
  navButton: {
    backgroundColor: 'rgb(33, 150, 243)',
    paddingVertical: 6,
    paddingHorizontal: 8,
    width: 100,
    marginBottom: 2,
    borderRadius: 50,
  },
  QRPopover: {
    backgroundColor: theme.TILE_BACKGROUND,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 15,
    borderWidth: 1,
  },
});

const QRCodeStyle = {
  padding: 12,
  background: '#FFFFFF',
  borderRadius: 12,
  marginTop: 12,
  marginBottom: 4,
};

function withParams(Component: any) {
  return (props: JSX.IntrinsicAttributes) => <Component {...props} params={useParams()} />;
}

export default withParams(BuilderEditor);
