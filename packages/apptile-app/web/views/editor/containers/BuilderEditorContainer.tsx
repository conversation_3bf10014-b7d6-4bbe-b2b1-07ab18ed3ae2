import {connect} from 'react-redux';
import {bindActionCreators} from 'redux';
import {changeAppConfig} from 'apptile-core';
import {AppDispatch} from '../../../../app/store';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {
  fetchOrgs,
  saveAppState,
  updateApp,
  editorCopyAction,
  editorPasteAction,
  configureDatasources,
  clearDatasourcesCredentials,
  softRestartConfig,
  changeAppContextData,
} from '../../../actions/editorActions';
import {initApptileTilesMode} from 'apptile-core';
import BuilderEditor from '../components/BuilderEditor';

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return bindActionCreators(
    {
      saveAppState,
      updateApp,
      changeAppConfig,
      fetchOrgs,
      editorCopyAction,
      editorPasteAction,
      configureDatasources,
      clearDatasourcesCredentials,
      softRestartConfig,
      changeAppContextData,
      initApptileTilesMode,
    },
    dispatch,
  );
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    editor: null,
    platform: state.platform,
    orgs: state.orgs,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(BuilderEditor);
