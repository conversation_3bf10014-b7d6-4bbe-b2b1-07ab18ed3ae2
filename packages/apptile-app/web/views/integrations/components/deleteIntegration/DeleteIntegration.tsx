import {
  toggleAppIntegration,
  getIntegrationTiles,
  closeDeleteIntegrationModal,
  toggleDeleteIntegrationModal,
} from '@/root/web/actions/editorActions';
import _, {isEmpty, set} from 'lodash';
import React, {useEffect, useState} from 'react';
import {ActivityIndicator, Image, Pressable, StyleSheet, View, Text} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';

import Button from '../../../../components-v2/base/Button';
import TextElement from '../../../../components-v2/base/TextElement';
import {EditorRootState} from '../../../../store/EditorRootState';
import IntegrationItemDetails from './IntegrationItemDetails';
import {Feather, MaterialCommunityIcons} from 'apptile-core';
import {useNavigate} from '@/root/web/routing.web';
import TextInput from '@/root/web/components-v2/base/TextInput';
import IntegrationTileInfo from './IntegrationTileInfo';

export const currentIntegrationSelector = (state: EditorRootState) => state.integration.currentIntegration;

const DeleteIntegration: React.FC<any> = (props: {tempPages: any[]}) => {
  const {tempPages} = props;
  const navigate = useNavigate();
  const integrationState = useSelector(currentIntegrationSelector);
  const {integration} = integrationState;
  const {tempTiles} = useSelector((state: EditorRootState) => state.pages) ?? null;
  const [isApplyTimer, setIsApplyTimer] = React.useState(false);
  const [currentStep, setCurrentStep] = React.useState(0);
  const [input, setInput] = useState('');
  const dispatch = useDispatch();

  const appId = _.get(integration, ['appIntegrations', 0, 'appId'], null);
  const id = _.get(integration, ['appIntegrations', 0, 'id'], null);
  const platformType = _.get(integration, ['appIntegrations', 0, 'platformType'], null);

  const removeIntegration = () => {
    setIsApplyTimer(true);
  };

  React.useEffect(() => {
    if (isApplyTimer === null) {
      dispatch(toggleAppIntegration(appId, id, false, platformType, tempPages));
      navigate(-1);
      dispatch(toggleDeleteIntegrationModal(false));
    }
  }, [isApplyTimer]);

  useEffect(() => {
    dispatch(getIntegrationTiles([platformType]));
  }, []);

  const handleBack = () => {
    dispatch(closeDeleteIntegrationModal());
  };

  const btnTextStyle = isEmpty(tempTiles?.data) ? styles.button : {};

  return (
    <View style={styles.container}>
      <Pressable onPress={handleBack} style={{position: 'absolute', right: 20, top: 20}}>
        <MaterialCommunityIcons size={24} name="close" color="#000000" />
      </Pressable>

      {integration?.icon && (
        <View style={styles.integrationIcon}>
          <Image source={{uri: integration?.icon}} resizeMode={'contain'} style={styles.cardContentIcon} />
        </View>
      )}
      <View style={[styles.flexOne]}>
        <TextElement color="SECONDARY" fontWeight="500" fontSize="2xl">
          Disconnecting {integration?.title ?? ''}
        </TextElement>
      </View>
      {tempTiles ? (
        <>
          {currentStep === 0 && (
            <>
              <View
                style={[
                  {
                    padding: 12,
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: 10,
                    borderRadius: 8,
                    borderColor: '#CE2029',
                    borderWidth: 1,
                    backgroundColor: 'rgba(206, 32, 41, 0.05)',
                    marginBottom: 15,
                  },
                ]}>
                <Feather name={'alert-triangle'} color={'#CE2029'} size={30} />
                <Text style={{fontSize: 14, fontFamily: 'Work Sans'}}>
                  Disconnecting will remove or replace app content related to this integration. Reconnecting later will
                  require a full setup.
                </Text>
              </View>
              <TextElement color="SECONDARY" fontWeight="400" fontSize="sm" style={{marginBottom: 12}}>
                To proceed, please type{' '}
                <TextElement color="SECONDARY" fontWeight="500" fontSize="sm">
                  “CONFIRM”
                </TextElement>{' '}
                in the box below:
              </TextElement>
              <TextInput
                containerStyle={styles.inputStyles}
                value={input}
                placeholder="Type “Confirm” to disconnect"
                onChange={(e: any) => {
                  setInput(e.target.value);
                }}
              />
              <View style={[styles.ctaBox]}>
                <Button
                  disabled={input.toLowerCase() !== 'confirm'}
                  onPress={() => {
                    setCurrentStep(1);
                  }}
                  variant={'TEXT'}
                  inversed
                  color="ERROR">
                  DISCONNECT
                </Button>
              </View>
            </>
          )}
          {!isEmpty(tempTiles.data) && currentStep == 1 && <IntegrationTileInfo tempTiles={tempTiles} />}
          {isEmpty(tempTiles.data) && currentStep == 1 && (
            <>
              <IntegrationItemDetails
                tempPages={tempPages}
                platformType={platformType}
                isAddIntegartion={false}
                isApplyTimer={isApplyTimer}
                setIsApplyTimer={setIsApplyTimer}
              />
              <View style={[styles.ctaBox]}>
                <Button
                  disabled={!isEmpty(tempTiles.data)}
                  onPress={removeIntegration}
                  variant={'TEXT'}
                  textStyles={btnTextStyle}
                  containerStyles={styles.disconnectStyle}>
                  DISCONNECT
                </Button>
              </View>
            </>
          )}
        </>
      ) : (
        <ActivityIndicator />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: 720,
    padding: 35,
  },
  inputStyles: {
    width: '100%',
    height: 35,
    borderRadius: 8,
  },
  ctaBox: {flex: 1, flexDirection: 'row', marginTop: 20, justifyContent: 'flex-end'},
  flexOne: {flex: 1, alignSelf: 'center', marginTop: 36},
  cardContentIcon: {position: 'relative', width: '50px', height: '50px', margin: 8, borderRadius: 8},
  integrationIcon: {flex: 1, alignSelf: 'center'},
  button: {fontWeight: '500', color: '#FF0000'},
  backTextStyle: {
    color: '#000000',
    fontWeight: '500',
    fontSize: 14,
  },
  disconnectStyle: {
    padding: 0,
    marginTop: 12,
  },
  backStyle: {
    padding: 0,
    marginLeft: -14,
  },
});

export default DeleteIntegration;
