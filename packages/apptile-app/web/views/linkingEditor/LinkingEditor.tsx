import React, {useCallback, useEffect, useState} from 'react';
import {Button, StyleSheet, View, Text} from 'react-native';
import {
  selectAppSettingsForKey,
  setAppSettings,
  updateSettingsValue,
  ILinkingItem,
  LINKING_SETTINGS_KEY,
  LINKING_SETTINGS_LINKS_KEY,
  LINKING_SETTINGS_PREFIXES_KEY,
  ILinkingConfig,
} from 'apptile-core';
import CollapsiblePanel from '../../components/CollapsiblePanel';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';
import LinkingItemListControl from './LinkingItemListControl';
import LinkingPrefixListControl from './LinkingPrefixListControl';

const linkingSettingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
const LinkingEditor: React.FC = props => {
  const settingsConfig = useSelector(linkingSettingsSelector(LINKING_SETTINGS_KEY));
  const prefixes: ILinkingConfig[LINKING_SETTINGS_PREFIXES_KEY] =
    settingsConfig.getSettingValue(LINKING_SETTINGS_PREFIXES_KEY);
  const links: ILinkingConfig[LINKING_SETTINGS_LINKS_KEY] = settingsConfig.getSettingValue(LINKING_SETTINGS_LINKS_KEY);
  const dispatch = useDispatch();

  const onPrefixesUpdated = useCallback(
    newPrefixes => {
      dispatch(updateSettingsValue(LINKING_SETTINGS_KEY, LINKING_SETTINGS_PREFIXES_KEY, newPrefixes));
    },
    [dispatch],
  );
  const onLinksUpdated = useCallback(
    newLinks => {
      dispatch(updateSettingsValue(LINKING_SETTINGS_KEY, LINKING_SETTINGS_LINKS_KEY, newLinks));
    },
    [dispatch],
  );

  return (
    <View style={styles.container}>
      <View style={{height: 'auto', flex: 1}}>
        <CollapsiblePanel iconName="link" iconType="Entypo" title="DeepLink Settings" isOpen={false}>
          <View style={{overflow: 'scroll', paddingTop: 8, paddingHorizontal: 4}}>
            <LinkingPrefixListControl prefixes={prefixes ?? []} onUpdate={onPrefixesUpdated} />
            <LinkingItemListControl links={links ?? []} onUpdate={onLinksUpdated} />
          </View>
        </CollapsiblePanel>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    paddingHorizontal: 5,
    flexBasis: 'auto',
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },
});
export default LinkingEditor;
