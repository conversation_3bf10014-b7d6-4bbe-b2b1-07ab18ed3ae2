import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, StyleSheet, TextInput, TouchableOpacity} from 'react-native';
import {useSelector} from 'react-redux';
import {debounce} from 'lodash';

import {MaterialCommunityIcons} from 'apptile-core';
import {ILinkingItem} from 'apptile-core';
import ScreenSelectorControl from '../../components/controls/ScreenSelectorControl';
import {pageConfigsSelector} from 'apptile-core';
import {selectScreens} from 'apptile-core';

export interface LinkingItemControlProps {
  linkingItem: ILinkingItem;
  onUpdate: (newItem: ILinkingItem) => void;
  onRemove: () => void;
}

const LinkingItemControl: React.FC<LinkingItemControlProps> = (props: LinkingItemControlProps) => {
  const pages = useSelector(pageConfigsSelector);
  const screens = useSelector(selectScreens);
  const {linkingItem, onUpdate, onRemove} = props;

  const screen = screens[linkingItem.screenName];
  const page = pages.get(screen?.screen)?.toJS();
  const pageParams = Object.entries(page?.pageParams ?? {});
  const allParams = pageParams.map(i => i[0]);
  const requiredParams = pageParams.filter(i => i[1].isRequired).map(i => i[0]);

  const handleChange = useCallback(
    (linkingURL: ILinkingItem['linkingURL']) => onUpdate({...linkingItem, linkingURL}),
    [linkingItem, onUpdate],
  );
  const debouncedHandleChange = useCallback(debounce(handleChange, 340), [handleChange]);
  const linkingURL = linkingItem.linkingURL || '';
  const [url, setUrl] = useState(linkingURL);
  const setUrlWithModalUpdate = (v: string) => {
    setUrl(v);
    debouncedHandleChange(v);
  };
  useEffect(() => setUrl(linkingURL), [linkingURL]);

  return (
    <View style={styles.container}>
      <View style={{height: 'auto', flex: 1}}>
        <View style={[styles.containerRow]}>
          <ScreenSelectorControl
            disableBinding={true}
            value={linkingItem?.screenName}
            label="Screen"
            onChange={(value: string) => onUpdate({...linkingItem, screenName: value})}
          />
          <TouchableOpacity onPress={() => onRemove()}>
            <MaterialCommunityIcons name="delete" size={14} color={'red'} />
          </TouchableOpacity>
        </View>
        <View style={[styles.containerRow]}>
          <Text style={styles.label}>URL</Text>
          <TextInput
            placeholder="path/:param"
            style={[styles.textInput]}
            value={url}
            onChange={e => setUrlWithModalUpdate(e.target.value)}
          />
        </View>
        {linkingItem?.linkingURL.slice(-1) === ':' && (
          <View style={[styles.containerRow]}>
            <Text style={styles.label}>Select parameter</Text>
            {allParams.map(p => (
              <TouchableOpacity
                key={p}
                style={styles.pill}
                onPress={() => onUpdate({...linkingItem, linkingURL: linkingItem.linkingURL + p + '/'})}>
                <Text style={styles.label}>{p}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
        <View>
          {linkingItem?.linkingURL.slice(0, 1) === '/' && <Text style={styles.error}>Remove leading slash!</Text>}
          {page ? null : <Text style={styles.error}>Invalid/No Screen Selected!</Text>}
          {page && !linkingItem?.linkingURL && <Text style={styles.error}>Url is required</Text>}
          {requiredParams.map(p =>
            !linkingItem?.linkingURL.includes(':' + p) ? (
              <Text key={p} style={styles.error}>
                {p} is required!
              </Text>
            ) : null,
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    padding: 5,
    borderWidth: 1,
    borderRadius: 4,
    borderColor: '#ddd',
    marginVertical: 4,
    flexBasis: 'auto',
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  label: {
    color: 'rgb(51, 51, 51)',
    fontSize: 10,
  },
  textInput: {
    marginLeft: 4,
    padding: 4,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ddd',
    width: '100%',
  },
  error: {
    color: 'red',
    fontSize: 11,
    marginRight: 2,
  },
  pill: {
    marginHorizontal: 2,
    borderRadius: 4,
    borderWidth: 1,
    padding: 2,
    borderColor: '#cccccc',
  },
});

export default LinkingItemControl;
