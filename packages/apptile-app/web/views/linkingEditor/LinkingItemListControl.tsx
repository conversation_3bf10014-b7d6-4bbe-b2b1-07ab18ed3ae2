import {ILinkingItem} from '@/root/app/common/datatypes/LinkingTypes';
import {MaterialCommunityIcons} from 'apptile-core';
import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import LinkingItemControl from './LinkingItemControl';
import commonStyles from '../../styles-v2/commonStyles';
import Button from '../../components-v2/base/Button';

export interface LinkingItemListControlProps {
  links: ILinkingItem[];
  onUpdate: (newLinks: ILinkingItem[]) => void;
}

const LinkingItemListControl: React.FC<LinkingItemListControlProps> = (props: LinkingItemListControlProps) => {
  const {links, onUpdate} = props;

  const addLink = useCallback(() => {
    const newLinks = links.concat({
      screenName: '',
      linkingURL: '',
      params: [],
    } as ILinkingItem);
    if (onUpdate) onUpdate(newLinks);
  }, [links, onUpdate]);
  const updateLink = useCallback(
    (newValue, index) => {
      const newLinks = links.slice();
      newLinks[index] = newValue;
      if (onUpdate) onUpdate(newLinks);
    },
    [onUpdate, links],
  );

  return (
    <View style={styles.container}>
      <Text style={[commonStyles.labelText, {marginBottom: 8, fontWeight: '500'}]}>Links</Text>
      <View style={{paddingLeft: 4}}>
        {(links ?? ([] as ILinkingItem[])).map((linkItem, idx) => (
          <LinkingItemControl
            key={idx}
            linkingItem={linkItem}
            onUpdate={i => updateLink(i, idx)}
            onRemove={() => onUpdate(links.filter(l => l.linkingURL !== linkItem.linkingURL))}
          />
        ))}
      </View>
      <Button
        onPress={addLink}
        color="PRIMARY"
        size="EXTRA-SMALL"
        variant="TEXT"
        innerContainerStyles={{justifyContent: 'flex-start'}}>
        Add Link
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    paddingLeft: 5,
    flexBasis: 'auto',
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },
  flex: {
    marginVertical: 4,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderWidth: 1,
    borderColor: '#0091BC',
    borderRadius: 8,
  },
});

export default LinkingItemListControl;
