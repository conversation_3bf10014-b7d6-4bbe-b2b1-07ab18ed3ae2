import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {View, Text, StyleSheet, TextInput} from 'react-native';
import commonStyles from '../../styles-v2/commonStyles';
import Button from '../../components-v2/base/Button';

export interface LinkingPrefixListControlProps {
  prefixes: string[];
  onUpdate: (newPrefixes: string[]) => void;
}

const LinkingPrefixListControl: React.FC<LinkingPrefixListControlProps> = (props: LinkingPrefixListControlProps) => {
  const {prefixes, onUpdate} = props;
  const [prefixList, setPrefixList] = useState(prefixes);
  useEffect(() => {
    if (!_.isEqual(prefixes, prefixList)) setPrefixList(prefixes ?? []);
  }, [prefixList, prefixes]);

  const addPrefix = useCallback(() => {
    const newPrefixes = prefixList.concat('newPrefix');
    setPrefixList(newPrefixes);
    if (onUpdate) onUpdate(newPrefixes);
  }, [onUpdate, prefixList]);
  const updatePrefix = useCallback(
    (newValue, index) => {
      const newPrefixes = prefixes.slice();
      newPrefixes[index] = newValue;
      setPrefixList(newPrefixes);
      if (onUpdate) onUpdate(newPrefixes);
    },
    [onUpdate, prefixes],
  );

  return (
    <View style={styles.container}>
      <Text style={[commonStyles.labelText, {fontWeight: '500'}]}>Linking Prefixes</Text>
      <View style={{height: 'auto', flex: 1, paddingHorizontal: 4}}>
        {prefixList?.map((prefixItem, idx) => (
          <TextInput
            style={[styles.textInput]}
            key={idx}
            onBlur={e => updatePrefix(e.target.value, idx)}
            defaultValue={prefixItem}
            blurOnSubmit={true}
            onSubmitEditing={e => updatePrefix(e.target.value, idx)}
          />
        ))}
      </View>
      <Button
        onPress={addPrefix}
        color="PRIMARY"
        size="EXTRA-SMALL"
        variant="TEXT"
        innerContainerStyles={{justifyContent: 'flex-start'}}>
        Add Prefix
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    paddingLeft: 5,
    flexBasis: 'auto',
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  textInput: {
    padding: 4,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ddd',
    marginVertical: 4,
  },
});

export default LinkingPrefixListControl;
