import { Lively<PERSON>pi } from '@/root/web/api/LivelyApi';
import { ILiveStreamSettings } from '@/root/web/api/LivelyApiTypes';
import TextElement from '@/root/web/components-v2/base/TextElement';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import commonStyles from '@/root/web/styles-v2/commonStyles';
import theme from '@/root/web/styles-v2/theme';
import React, { useEffect } from 'react'
import { Image, Pressable, StyleSheet, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { fetchInstagramPages } from '@/root/web/actions/liveSellingActions';
import { Icon } from 'apptile-core';

const InstagramSettings = ({
  settings,
  setSettings
}: {
  settings: ILiveStreamSettings;
  setSettings: (settings: ILiveStreamSettings) => void;
}) => {

  const dispatch = useDispatch();
  const queryParams = new URLSearchParams(window.location.search);
  const instaAuthCode = queryParams.get('code');
  const instaAuthToken = queryParams.get('state') ? queryParams.get('token') : '';

  const authToken = useSelector((state: EditorRootState) => state.liveSelling.auth.authToken);
  const apptileAppId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);
  const instagramLoading = useSelector((state: EditorRootState) => state.liveSelling.instagramChannelLoading);
  const instagramFound = useSelector((state: EditorRootState) => state.liveSelling.instagramChannelFound);
  const instagramChannels = useSelector((state: EditorRootState) => state.liveSelling.instagramChannelAuths);
  const isInstagramConnectionError = useSelector((state: EditorRootState) => state.liveSelling.isInstagramConnectionNetworkError);

  const handleLoginInstagram = () => {
    window.open(
      `https://www.instagram.com/oauth/authorize?client_id=***************&redirect_uri=${window.location.href}&scope=business_basic%2Cbusiness_manage_messages%2Cbusiness_manage_comments%2Cbusiness_content_publish&response_type=code&logger_id=407cd1da-4a6b-452e-be97-897cd9cd9d9b`,
      '_self',
    );
  }
  const checkInstaRtmpToken = async (account_handle: string) => {
    try {
      const response = await LivelyApi.checkInstagramSession(authToken, account_handle);
      if (response?.data?.token && response?.data?.access_token) {
        alert('Authentication Successfull.');
      } else {
        alert('Token is invalid, Please Login Again.');
        const instaStateParamObject = {
          user_name: account_handle,
          redirect: `${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}&user-name=${account_handle}`,
        };
        const encodedStateParam = base64EncodedUnicodeInstagram(instaStateParamObject);
        const params = new URLSearchParams();
        params.set('state', encodedStateParam);
        setTimeout(() => {
          window.open(`https://app.rtmp.in/?auth_partner_id=oe1Gk8sh5jRVdFlwyg2Wb&${params.toString()}&beta=1`, '_self');
        }, 100);
      }
    } catch (error) {
      const instaStateParamObject = {
        user_name: account_handle,
        redirect: `${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}&user-name=${account_handle}`,
      };
      const encodedStateParam = base64EncodedUnicodeInstagram(instaStateParamObject);
      const params = new URLSearchParams();
      params.set('state', encodedStateParam);
      setTimeout(() => {
        window.open(`https://app.rtmp.in/?auth_partner_id=oe1Gk8sh5jRVdFlwyg2Wb&${params.toString()}&beta=1`, '_self');
      }, 100);
    }
  };
  const base64EncodedUnicodeInstagram = (obj: any) => {
    const jsonString = JSON.stringify(obj);
    const textEncoder = new TextEncoder();
    const uint8Array = textEncoder.encode(jsonString);
    const base64EncodedUnicode = btoa(String.fromCharCode(...uint8Array));
    const finalBase64EncodeUrl = base64EncodedUnicode.replace(/\+/g, '-').replace(/\//g, '_').replace(/[=]+$/, '');
    return finalBase64EncodeUrl;
  };

  useEffect(() => {
    if (authToken) dispatch(fetchInstagramPages());
    if (instaAuthCode && authToken) {
      LivelyApi.updateInstagramStatus(authToken, instaAuthCode)
        .then(response => {
          const account_handle = response?.data?.data?.account_handle;
          if (account_handle) checkInstaRtmpToken(account_handle);
          else window.open(`${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}`, '_self');
        })
        .catch(err => {
          console.log(err);
          window.open(`${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}`, '_self');
        });
    }
    if (instaAuthToken && authToken && queryParams.get('user-name')) {
      LivelyApi.setInstagramToken(authToken, queryParams.get('user-name'), instaAuthToken)
        .then(response => {
          // console.log(response);
          dispatch(fetchInstagramPages());
        })
        .catch(err => {
          console.log(err);
          window.open(`${window.location.origin}${window.location.pathname}?app-id=${apptileAppId}`, '_self');
        });
    }
  }, [])

  return (
    <View style={styles.container}>
      <View style={styles.streamSettings}>
        <View>
          <TextElement
            color='SECONDARY'
            fontSize="md"
            lineHeight="md"
            fontWeight="500"
          >
            Instagram Streaming
          </TextElement>
          <TextElement
            style={{color: theme.CONTROL_PLACEHOLDER_COLOR, marginTop: 4}}
            fontSize='xs'
            lineHeight='xs'
            fontWeight='400'
          >
            Select a facebook page for streaming your live shows.
          </TextElement>
        </View>
        <View style={{marginTop: 15, flexDirection: 'row', flexWrap: 'wrap'}}>
          {instagramLoading && (
            <Image
              source={require('@/root/web/assets/images/preloader.svg')}
              style={styles.loaderImage}
            />
          )}
          {!instagramLoading && (!instagramFound || instagramChannels.length < 1) && (
            <Pressable onPress={handleLoginInstagram} style={styles.instagramButton}>
              <TextElement
                fontSize="sm"
                lineHeight="sm"
                fontWeight="400"
                style={{color: 'white'}}
              >
                Connect with Instagram
              </TextElement>
              <Icon color={'#fff'} name="instagram" size={20} iconType={'MaterialCommunityIcons'} />
            </Pressable>
          )}
          {!instagramLoading &&
            instagramFound &&
            instagramChannels.map((e: any) => (
              <Pressable
                onPress={() =>
                  settings?.streamToInstagramAuthId?.includes(e._id)
                    ? setSettings({...settings, streamToInstagramAuthId: ''})
                    : setSettings({...settings, streamToInstagramAuthId: e.id})
                }
                style={[{
                  width: 100,
                  overflow: 'hidden',
                  alignItems: 'center',
                  borderRadius: 8,
                  backgroundColor: settings?.streamToInstagramAuthId?.includes(e._id)
                    ? '#D2E3FC'
                    : theme.CONTROL_BORDER,
                  paddingVertical: 10,
                  paddingHorizontal: 20,
                  margin: theme.PRIMARY_MARGIN
                }, settings.streamToInstagramAuthId?.includes(e._id) && {boxShadow: '0px 2px 2px rgba(0, 0, 0, 0.25)'}]}>
                <View>
                  {e.profile_url ? (
                    <Image
                      source={{uri: e.profile_url}}
                      style={{width: 60, height: 60, borderRadius: 80}}
                      resizeMode="cover"
                    />
                  ) : (
                    <View
                      style={{
                        width: 60,
                        height: 60,
                        borderRadius: 80,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: '#fff',
                      }}>
                      <TextElement style={[commonStyles.baseText, {fontSize: 24}]}>
                        {(e?.account_handle?.[0] || '').toUpperCase()}
                      </TextElement>
                    </View>
                  )}
                  <Image
                    source={require('@/root/web/assets/images/logos_instagram.png')}
                    style={{width: 20, height: 20, position: 'absolute', bottom: 0, right: 0}}
                    resizeMode="contain"
                  />
                </View>
                <TextElement
                  style={{
                    lineBreak: 'anywhere',
                    textAlign: 'center',
                    fontSize: 12,
                    marginTop: 10,
                  }}>
                  {e.account_handle}
                </TextElement>
              </Pressable>
            ))}
        </View>
      </View>
    </View>
  )
}

export default InstagramSettings

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
  streamSettings: {
    width: '100%',
  },
  instagramButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    backgroundColor: '#ff2255',
  },
});