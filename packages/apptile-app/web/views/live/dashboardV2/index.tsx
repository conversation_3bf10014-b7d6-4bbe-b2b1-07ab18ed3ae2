import { COMMON_ERROR_MESSAGE } from '@/root/app/common/utils/apiErrorMessages/generalMessages';
import { LOGIN_MESSAGES } from '@/root/app/common/utils/apiErrorMessages/specificFeature';
import User<PERSON>pi from '@/root/web/api/UserApi';
import { builderEmails, whitelistedEmails } from '@/root/web/common/featureGatingConstants';
import { selectCurrentPlanWithDetails } from '@/root/web/selectors/BillingSelector';
import theme from '@/root/web/styles-v2/theme';
import { planFeaturesList } from 'apptile-core';
import axios from 'axios';
import _ from 'lodash-es';
import LogRocket from 'logrocket';
import React, { useEffect, useRef, useState } from 'react';
import { Image, ScrollView, StyleSheet, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { fetchMySubscription, fetchPlanList, setEditorFeatures, setOpenPremiumModal, USER_INIT_FETCHED } from '../../../actions/editorActions';
import { fetchActiveStreams, fetchFacebookPages, fetchStreamSettings, updateCreateStreamMeta } from '../../../actions/liveSellingActions';
import { makeToast } from '../../../actions/toastActions';
import ModalComponent from '../../../components-v2/base/Modal';
import { Outlet, useLocation, useNavigate } from '../../../routing.web';
import { checkApptileEmailSelector, openPremiumModalSelector } from '../../../selectors/FeatureGatingSelector';
import { EditorRootState } from '../../../store/EditorRootState';
import PremiumUpgradeModal from '../../featureGating/premiumUpgradeModal';
import { DashboardUpgradeModal } from '../modals/dashboardUpgradeModal';
import { geteErrorMessage, handleToast } from '../shared/CommonError';
import NetworkErrorPage from '../shared/NetworkErrorPage';
import { LIVE_DASHBOARD_ROUTES } from '../utils/liveDashboardRoutes';
import { initZego, zegoExpEngine } from '../zegoEngine';
import LiveSideBar from './LiveSideBar';

export enum STREAM_TYPE {
  LIVE = 'live',
  AUCTION = 'auction',
}

const DashboardV2 = () => {
  const authToken = useSelector(state => state.liveSelling.auth.authToken);
  const livelyUser = useSelector(state => state.liveSelling.auth.livelyUser);
  const createStremMeta = useSelector((state: EditorRootState) => state.liveSelling.streamCreationMeta);
  const isUpcomingNetworkError = useSelector(state => state.liveSelling.upcomingStreams.isNetworkError);
  const isInPreogressNetworkError = useSelector(state => state.liveSelling.upcomingStreams.isNetworkError);
  const isPastetworkError = useSelector(state => state.liveSelling.upcomingStreams.isNetworkError);
  const apptileAppId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);
  const currentPlan = useSelector(selectCurrentPlanWithDetails);
  const openPremiumModal = useSelector(openPremiumModalSelector);
  const dashboardCheckIntervalRef = useRef(null);

  /* Variables */
  const basePlan = currentPlan?.basePlan ?? currentPlan;
  let editorFeatures =
    _.find(planFeaturesList, (e: any) => e.serverCode === basePlan?.name)?.allowedFeatures ??
    planFeaturesList.CORE.allowedFeatures;

  const {userFetched, user} = useSelector((state: EditorRootState) => state.user);
  const isApptileUser = useSelector(checkApptileEmailSelector);
  if (userFetched && user?.email && typeof user?.email === 'string') {
    if (isApptileUser || whitelistedEmails.includes(user?.email) || builderEmails.includes(user?.email)) {
      editorFeatures = planFeaturesList.ENTERPRISE.allowedFeatures;
    }
  }

  /* States */
  const [playAnimation, setPlayAnimation] = useState(false);
  const [showDashboardUpgradeModal, setShowDashboardUpgradeModal] = useState(false);

  /* Hooks */
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();

  /* Functions */
  const onPremiumModalVisibilityChange = (value: boolean) => {
    if (!value) {
      dispatch(setOpenPremiumModal(false, ''));
    }
  };
  const getUser = async () => {
    try {
      const response = await UserApi.fetchUserInit();
      const userData = response.data;
      dispatch({
        type: USER_INIT_FETCHED,
        payload: userData,
      });
    } catch (err) {
      const errorMessage = geteErrorMessage(err, "getUser - Login")
      if(errorMessage !== COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR){
        handleToast(errorMessage, dispatch)
      }
    }
  };
  const checkDashboard = () => {
    const url = window.location.href;
    axios
      .get(url.replace(window.location?.pathname, ''), {
        headers: {
          Pragma: 'no-cache',
          Expires: -1,
          'Cache-Control': 'no-cache',
        },
      })
      .then(function (rawData) {
        const htmlData = rawData.data;
        const regex = new RegExp('((BUILDNUMBERSTART---)([\\S]{1,})(---BUILDNUMBEREND))', 'gmi');
        const buildTag = htmlData.match(regex)?.[0]?.slice(19, -17);
        if (!buildTag) {
          console.log('Not able to check build number.');
        } else {
          if (buildTag != (window as any).PLATFORM_BUILD_NUMBER) {
            setShowDashboardUpgradeModal(true);
          } else {
            console.log('Dashboard is on latest tag');
          }
        }
      })
      .catch(function (err) {
        console.log('Error while checking latest dashboard version', err);
      });
  };

  /* Effects */
  useEffect(() => {
    if (!authToken) dispatch(fetchPlanList());
    if (apptileAppId && authToken) {
      console.log('Build - ', (window as any).BN_FOR_LOGROCKET);
      dispatch(fetchMySubscription(apptileAppId as string));
      getUser();
    }
  }, [authToken, apptileAppId, dispatch]);

  useEffect(() => {
    dispatch(setEditorFeatures(editorFeatures));
  }, [dispatch, editorFeatures, authToken, apptileAppId]);
  useEffect(() => {
    if(authToken) {
      dispatch(fetchActiveStreams());
      dispatch(fetchStreamSettings());
      dispatch(fetchFacebookPages());
    }
  }, [authToken, dispatch]);
  useEffect(() => {
    if (createStremMeta?.error) {
      dispatch(
        makeToast({
          content: createStremMeta?.error,
          appearances: 'error',
          duration: 3000,
        }),
      );
    }
    return () => {
      dispatch(updateCreateStreamMeta({data: '', error: null}));
    };
  }, [createStremMeta?.error, dispatch]);
  useEffect(() => {
    if (apptileAppId) {
      dispatch(fetchPlanList());
      console.log('Build - ', (window as any).BN_FOR_LOGROCKET);
      dispatch(fetchMySubscription(apptileAppId as string));
      getUser();
    }
  }, [apptileAppId, dispatch]);
  useEffect(() => {
    dispatch(setEditorFeatures(editorFeatures));
  }, [dispatch, editorFeatures]);
  useEffect(() => {
    if (livelyUser) {
      if(!zegoExpEngine){
        try {
          initZego();
        } catch (err) {
          dispatch(
            makeToast({
              content: LOGIN_MESSAGES.ERROR.ZEGO_INIT_FAILED,
              appearances: 'error',
            }),
          );
        }
      }
      const identity = {
        name: livelyUser.user_name,
        company: livelyUser.company_name,
        buildnumber: (window as any).BN_FOR_LOGROCKET,
      };
      LogRocket.identify(livelyUser.user_id, identity);
    }
  }, [livelyUser, dispatch]);
  useEffect(() => {
    if(location.pathname === LIVE_DASHBOARD_ROUTES.DASHBOARD()) {
      navigate(LIVE_DASHBOARD_ROUTES.OVERVIEW_LIVE());
    }
  }, [location.pathname, navigate]);
  useEffect(() => {
    // 5 Min Interval
    checkDashboard();
    dashboardCheckIntervalRef.current = setInterval(checkDashboard, 5 * 60 * 1000);
    return async () => {
      clearInterval(dashboardCheckIntervalRef.current);
      dashboardCheckIntervalRef.current = null;
    };
  }, []);

  if(isUpcomingNetworkError || isInPreogressNetworkError || isPastetworkError) {
    return <NetworkErrorPage />
  }

  return (
    <>
      {authToken ? (
        <>
          <View key={authToken} style={[styles.container]}>
            <LiveSideBar
              playAnimation={playAnimation}
              setPlayAnimation={setPlayAnimation}
            />
            <ScrollView style={{width: '100%'}} contentContainerStyle={styles.wrapper}>
              <Outlet context={{playAnimation, setPlayAnimation}}/>
            </ScrollView>
            {openPremiumModal && (
              <ModalComponent
                onVisibleChange={onPremiumModalVisibilityChange}
                visible={openPremiumModal}
                content={<PremiumUpgradeModal />}
              />
            )}
            {showDashboardUpgradeModal && (
              <ModalComponent
                onVisibleChange={setShowDashboardUpgradeModal}
                visible={showDashboardUpgradeModal}
                content={
                  <DashboardUpgradeModal
                    onConfirmPress={() => {
                      setShowDashboardUpgradeModal(false);
                    }}
                  />
                }
              />
            )}
          </View>
        </>
      ) : (
        <Image source={require('@/root/web/assets/images/preloader-blue.svg')} style={{width: 50, height: 50}} />
      )}
    </>
  );
};

export default DashboardV2;

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    flex: 1,
    flexDirection: 'row',
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    padding: 30,
    flex: 1
  },
});
