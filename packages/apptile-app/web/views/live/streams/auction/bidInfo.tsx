import TextElement from '@/root/web/components-v2/base/TextElement';
import React, { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Pressable, StyleSheet, View } from 'react-native';
import moment from 'moment';
import theme from '@/root/web/styles-v2/theme';
import { useSelector } from 'react-redux';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import { BidEvent } from '../zeego/eventTypes';

interface BidInfoProps {
  role: number;
  isLoggedIn: boolean;
  highestBid: number;
  selectedProductId: string;
  cancelBidding: () => void;
  startBidding: () => void;
  closeBidding: () => void;
  endTimeStamp: number;
  biddingStartTimeStamp: number;
}

function formatTime(ms: number) {
  if(!ms || isNaN(ms)) return '00h : 00m : 00s';
  const totalSeconds = Math.ceil(ms / 1000);
  const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');
  const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, '0');
  const seconds = String(totalSeconds % 60).padStart(2, '0');
  return `${hours}h : ${minutes}m : ${seconds}s`;
}

const BidInfo: React.FC<BidInfoProps> = ({
  role,
  isLoggedIn,
  highestBid, 
  selectedProductId,
  cancelBidding,
  startBidding,
  closeBidding,
  endTimeStamp,
  biddingStartTimeStamp,
}: BidInfoProps) => {

  const currencyCode = useSelector((state: EditorRootState) => state.liveSelling.auth.livelyUser?.currency?.currency_code);

  const [bidClosed, setBidClosed] = useState(false);
  const [bidCanceled, setBidCanceled] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const timerRef = useRef<any>(null);

  const handleBidClose = (type: BidEvent) => {
    clearInterval(timerRef.current!);
      if(type == BidEvent.CLOSE) {
      closeBidding();
      setBidClosed(true);
    }
    if(type == BidEvent.CANCEL) {
      cancelBidding();
      setBidCanceled(true);
    }
    setTimeout(() => {
      setBidClosed(false)
      setBidCanceled(false)
    }, 5000)
  }

  useEffect(() => {
    if(!endTimeStamp || !biddingStartTimeStamp) return;
    if(timerRef.current) clearTimeout(timerRef.current);
    const timeOffset = biddingStartTimeStamp - Date.now();
    const tick = () => {
      const adjustedNow = Date.now() + timeOffset;
      const timeRemaining = endTimeStamp - adjustedNow;
      setTimeLeft(Math.max(0, timeRemaining))
      // Waiting extra 1000 ms to process any last second snipping bids
      if(timeRemaining > -1000) timerRef.current = setTimeout(tick, 250) 
      else handleBidClose(BidEvent.CLOSE)
    }
    tick();
    return () => timerRef.current && clearTimeout(timerRef.current);
  }, [endTimeStamp, biddingStartTimeStamp]);

  return (
    <View style={styles.container}>
      <View style={styles.headingContainer}>
        <TextElement fontSize='md' fontWeight='600' color='SECONDARY'>Bidding Stats</TextElement>
        {role == 2 && (
          !isLoggedIn ? (
            <ActivityIndicator size={20} />
          ) : (
            (!bidClosed && !bidCanceled) && (
              endTimeStamp ? (
                <Pressable onPress={() => handleBidClose(BidEvent.CANCEL)} style={[styles.commonButtonContainer, {borderColor: '#FF5050'}]}>
                  <TextElement fontSize='xs' style={{color: '#FF5050'}}>Cancel Bidding</TextElement>
                </Pressable>
              ) : (
                <Pressable onPress={startBidding} style={[styles.commonButtonContainer]}>
                  <TextElement fontSize='xs' style={{color: theme.PRIMARY_COLOR_DARK}}>Start Bidding</TextElement>
                </Pressable>
              )
            )
          )
        )}
      </View>

      <View style={{paddingHorizontal: 24, paddingVertical: 20}}>
        {bidClosed ? (
          <View>
            <TextElement fontSize='xs' fontWeight='400' style={{color: '#7D7D7D'}}>
              Anouncing winners to the Audience
            </TextElement>
          </View>
        ) : bidCanceled ? (
          <View>
            <TextElement fontSize='xs' fontWeight='400' style={{color: '#FF5050'}}>
              Bidding Cancelled
            </TextElement>
          </View>
        ) : !(endTimeStamp > 0) ? (
            <View>
              <TextElement fontSize='xs' fontWeight='400' style={{color: '#7D7D7D'}}>
                {selectedProductId ? (
                  role == 2 ? 'Start bidding for a product to view the bidding stats' : 'Bidding has not started yet'
                ) : (
                  role == 2 ? 'Show a product first to start bidding' : 'Bidding has not started yet'
                )}
              </TextElement>
            </View>
          ) : (
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
              <TextElement fontSize="md" fontWeight="600">
                Highest Bid:
                {highestBid ? (
                  ` ${highestBid.toFixed(2)} ${currencyCode}`
                ) : (
                  ' Yet to bid'
                )}
              </TextElement>
              <TextElement fontSize="sm" fontWeight="500" color='SECONDARY'>
                {formatTime(timeLeft)}
              </TextElement>
            </View>
          )}
      </View>
    </View>
  );
};

export default BidInfo;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#CDCDCD'
  },
  commonButtonContainer: {
    borderWidth: 1,
    borderRadius: 24,
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderColor: theme.PRIMARY_COLOR_DARK
  },
  headingContainer: {
    borderBottomWidth: 1, 
    borderBottomColor: theme.INPUT_BORDER, 
    paddingVertical: 16, 
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  }
})