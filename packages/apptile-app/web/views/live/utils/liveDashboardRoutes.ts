import {STREAM_TYPE} from '../dashboardV2';

export const LIVE_DASHBOARD_ROUTES = {
  OVERVIEW_LIVE: () => `/live-selling/dashboard/live`,
  OVERVIEW_AUCTION: () => `/live-selling/dashboard/auction`,

  ALL_STREAMS: () => `/live-selling/dashboard/live/streams`,
  ALL_AUCTIONS: () => `/live-selling/dashboard/auction/auctions`,

  LIVE_VIDEO_CLIPS: () => `/live-selling/dashboard/live/video-clips`,

  LIVE_ANALYTICS: () => `/live-selling/dashboard/live/analytics`,
  AUCTION_ANALYTICS: () => `/live-selling/dashboard/auction/analytics`,

  LIVE_STREAM_ANALYTICS: (streamId: string) => `/live-selling/dashboard/live/analytics/${streamId}`,
  LIVE_AUCTION_ANALYTICS: (streamId: string) => `/live-selling/dashboard/auction/analytics/${streamId}`,

  SETTINGS: () => `/live-selling/dashboard/settings/`,

  CREATE_STREAM: (apptileAppId: string, streamType: string) =>
    `/live-selling/create-stream?stream-type=${streamType}&app-id=${apptileAppId}`,
  EDIT_STREAM: (streamId: string, streamType: string) =>
    `/live-selling/edit-stream/${streamId}?stream-type=${streamType}`,

  STREAM: (streamId: string, role: number, streamType: string) => `/live-selling/${streamType}/${streamId}/${role}`,

  LOGIN: () => `/live-selling/login`,
  DASHBOARD: () => `/live-selling/dashboard`,

  LIVE_PAST_STREAMS: () => `/live-selling/dashboard/live/streams/past`,
  AUCTION_PAST_STREAMS: () => `/live-selling/dashboard/auction/auctions/past`,
};
