import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, View, Text} from 'react-native';
import CollapsiblePanel from '../../../components/CollapsiblePanel';
import ModelThemes from './ModelThemes';
import JSONTree from 'react-native-json-tree';
import _ from 'lodash';
import CodeInputControl from '@/root/web/components/controls/CodeInputControl';
import {NAMESPACE_SPERATOR} from 'apptile-core';

interface ModelBrowserProps {
  model: any;
  rawData: boolean;
  valueToRender: string;
}

const ModelBrowserElement: React.FC<ModelBrowserProps> = ({model, rawData = false, valueToRender = 'jsModel'}) => {
  const [query, setQuery] = useState('');
  const [modelJSON, setModelJSON] = useState({});
  const [filteredModel, setFilteredModel] = useState({});
  // Themes Check
  // let themes=[];
  // for(var i in ModelThemes){
  //   themes.push(<View style={{overflow:"scroll"}}><JSONTree data={{modelSeprated,theme:i}||{}} hideRoot={true} theme={ModelThemes[i]} invertTheme={true} /></View>)
  // }

  const deepSearch = useCallback((rawObject: any, query: string, depth = 0) => {
    let check = false;
    const object = depth == 0 ? _.cloneDeep(rawObject) : rawObject;
    if (Array.isArray(object) && typeof object[0] !== 'object') {
      return null;
    }
    for (let i in object) {
      if (i.trim().toLowerCase().indexOf(query.trim().toLowerCase()) > -1) {
        object[i] = object[i];
        check = true;
      } else if (typeof object[i] === 'object') object[i] = deepSearch(object[i], query, depth + 1);
      else delete object[i];

      if (object[i] == null) delete object[i];
      else check = true;
    }
    return check ? object : null;
  }, []);

  useEffect(() => {
    if (query.trim() == '') setFilteredModel(modelJSON);
    setFilteredModel(deepSearch(modelJSON, query) ?? {});
  }, [query, modelJSON, deepSearch]);

  useEffect(() => {
    if (rawData) {
      setModelJSON(model.get(valueToRender));
    } else {
      const modelJS = model?.get(valueToRender);
      const modelSeprated = {globals: {}, pages: {}};
      const namespaces = {};
      for (let i in modelJS) {
        if (modelJS[i].type != 'navigator') {
          if (modelJS[i].pageKey) {
            modelSeprated.pages[modelJS[i].pageId] = _.assign({}, modelJS[i]);
            delete modelSeprated.pages[modelJS[i].pageId].plugins;
            let components = {queries: {}, widgets: {}, tiles: {}};
            for (let j in modelJS[i].plugins) {
              if (modelJS[i].plugins[j].pluginType == 'QueryPlugin') components.queries[j] = modelJS[i].plugins[j];
              else if (modelJS[i].plugins[j].pluginType == 'ModuleInstance') {
                namespaces[modelJS[i].plugins[j].childNamespace] = j;
                if (!components.tiles[j]) components.tiles[j] = {};
                components.tiles[j] = {...components.tiles[j], ...modelJS[i].plugins[j], children: {}};
              } else {
                if (j.indexOf(NAMESPACE_SPERATOR) > -1 && namespaces[j.split(NAMESPACE_SPERATOR).slice(-2)[0]]) {
                  if (!components.tiles[namespaces[j.split(NAMESPACE_SPERATOR).slice(-2)[0]]]) {
                    components.tiles[namespaces[j.split(NAMESPACE_SPERATOR).slice(-2)[0]]] = {children: {}};
                  }
                  components.tiles[namespaces[j.split(NAMESPACE_SPERATOR).slice(-2)[0]]].children[j] =
                    modelJS[i].plugins[j];
                } else {
                  components.widgets[j] = modelJS[i].plugins[j];
                }
              }
            }
            modelSeprated.pages[modelJS[i].pageId].components = components;
          } else modelSeprated.globals[i] = modelJS[i];
        }
      }
      setModelJSON(modelSeprated);
    }
  }, [model, rawData, valueToRender]);

  return (
    <View style={{overflow: 'scroll'}}>
      {/* {themes} */}
      <View style={{paddingHorizontal: 6, paddingTop: 10}}>
        <CodeInputControl
          value={query}
          placeholder="Search Component"
          onChange={function (value: string): void {
            setQuery(value);
          }}
        />
      </View>
      <JSONTree
        data={filteredModel}
        hideRoot={true}
        theme={ModelThemes.hopscotch}
        invertTheme={true}
        labelRenderer={raw => {
          return (
            <Text
              onClick={(event: any) => {
                if (event.detail == 2 && raw.includes('components')) {
                  const path = raw
                    .slice(0, raw.indexOf('components') - 1)
                    .reverse()
                    .map(e => (!isNaN(e) ? `[${e}]` : e))
                    .join('.')
                    .replace(/\.\[/gim, '[');
                  navigator.clipboard.writeText(
                    path.indexOf(NAMESPACE_SPERATOR) > -1
                      ? path.slice(path.indexOf(NAMESPACE_SPERATOR) + NAMESPACE_SPERATOR.length)
                      : path,
                  );
                } else if (event.detail == 2 && raw.includes('params')) {
                  navigator.clipboard.writeText(
                    'currentPage.' +
                      raw
                        .slice(0, raw.indexOf('params') + 1)
                        .reverse()
                        .map(e => (!isNaN(e) ? `[${e}]` : e))
                        .join('.')
                        .replace(/\.\[/gim, '['),
                  );
                } else if (event.detail == 2 && raw.includes('globals')) {
                  navigator.clipboard.writeText(
                    raw
                      .slice(0, raw.indexOf('globals'))
                      .reverse()
                      .map(e => (!isNaN(e) ? `[${e}]` : e))
                      .join('.')
                      .replace(/\.\[/gim, '['),
                  );
                }
              }}>
              {`${raw[0]}: `}
            </Text>
          );
        }}
        valueRenderer={raw => {
          return (
            <Text
              onClick={event => {
                if (event.detail == 2) {
                  navigator.clipboard.writeText(isNaN(raw) && raw.startsWith('"') ? raw.slice(1, -1) : raw);
                }
              }}>
              {raw}
            </Text>
          );
        }}
      />
    </View>
  );
};

const ModelBrowser: React.FC<ModelBrowserProps> = ({model, rawData, valueToRender, title}) => {
  return (
    <View style={styles.container}>
      <View style={{height: 'auto', flex: 1}}>
        <CollapsiblePanel iconName="database-outline" title={title} isOpen={false}>
          <ModelBrowserElement model={model} rawData={rawData} valueToRender={valueToRender} />
        </CollapsiblePanel>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    paddingHorizontal: 5,
    flexBasis: 'auto',
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },

  actionLinkBox: {
    flexDirection: 'row',
    margin: 2,
    borderRadius: 4,
  },
  actionLinkText: {textAlign: 'right', flex: 1, padding: 5, color: '#3c92dc'},
});

export default ModelBrowser;
