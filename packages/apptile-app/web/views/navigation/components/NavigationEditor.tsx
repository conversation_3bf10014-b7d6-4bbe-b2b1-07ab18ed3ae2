import React, {} from 'react';
import {StyleSheet, View} from 'react-native';
import {useDispatch} from 'react-redux';
import {AppConfig} from 'apptile-core';
import CollapsiblePanel from '../../../components/CollapsiblePanel';
import NavigationEditorNav from './NavigationEditorNav';

interface NavigationEditorProps {
  appConfig: AppConfig;
  selectNavigation: () => void;
}

const NavigationEditor: React.FC<NavigationEditorProps> = ({appConfig, selectNavigation}) => {
  const dispatch = useDispatch();
  const navigationConfig = appConfig?.get('navigation');
  const pages = appConfig?.get('pages');
  const rootNavigator = navigationConfig?.get('rootNavigator');

  return (
    <View style={styles.container}>
      <View style={{height: 'auto', flex: 1}}>
        <CollapsiblePanel title="Navigation" iconName={'mobile'} iconType={'Entypo'} isOpen={false}>
          {rootNavigator && <NavigationEditorNav navigator={rootNavigator} pathSelector={['/']} pages={pages}/>}
        </CollapsiblePanel>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    paddingHorizontal: 5,
    flexBasis: 'auto',
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },

  actionLinkBox: {
    flexDirection: 'row',
    margin: 2,
    borderRadius: 4,
  },
  actionLinkText: {textAlign: 'right', flex: 1, padding: 5, color: '#3c92dc'},
});

export default NavigationEditor;
