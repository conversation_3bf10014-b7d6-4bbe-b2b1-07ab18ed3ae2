import React from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {useDispatch} from 'react-redux';
import {addNavigationNav, addNavigationPage} from 'apptile-core';
import {NavigatorConfig, PageConfig} from 'apptile-core';
import {useCallbackRef} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import {EDITOR_SELECT_NAV_COMPONENT, navigationReodering} from '../../../actions/editorActions';
import NavigationEditorScreen from './NavigationEditorScreen';

interface NavigationEditorNavProps {
  navigator: NavigatorConfig;
  pathSelector: string[];
  pages: PageConfig;
}

const NavigationEditorNav: React.FC<NavigationEditorNavProps> = ({navigator, pathSelector, pages}) => {
  const dispatch = useDispatch();
  const navCallBack = useCallbackRef(() => {
    dispatch({
      type: EDITOR_SELECT_NAV_COMPONENT,
      payload: pathSelector,
    });
  });
  const addPage = useCallbackRef(() => {
    let i = 1;
    let scrName = `Screen${i}`;
    const screenNames = navigator.screens.keySeq().toArray();
    while (true) {
      if (!screenNames.includes(scrName)) break;
      i++;
      scrName = `Screen${i}`;
    }
    dispatch(addNavigationPage(pathSelector, scrName));
  });
  const addNav = useCallbackRef(() => {
    let i = 1;
    let navName = `Nav${i}`;
    const screenNames = navigator.screens.keySeq().toArray();
    while (true) {
      if (!screenNames.includes(navName)) break;
      i++;
      navName = `Nav${i}`;
    }
    dispatch(addNavigationNav(pathSelector, navName, 'stack'));
  });

  const handleNavigationReordering = (operation: string, path: string[]) => {
    return () => {
      dispatch(navigationReodering(operation, path));
    };
  };

  return (
    <View style={styles.container}>
      <View style={{height: 'auto', flex: 1}}>
        <View style={[styles.displayRow]}>
          <Pressable onPress={navCallBack} style={styles.containerRow}>
            <MaterialCommunityIcons name="folder" color={'#333'} size={15} />
            <Text>
              {navigator.name} ({navigator.navigatorType})
            </Text>
          </Pressable>
          <View style={styles.containerRowRight}>
            <Pressable onPress={addNav}>
              <MaterialCommunityIcons name="folder-plus-outline" color={'#333'} size={15} />
            </Pressable>
            <Pressable onPress={addPage} style={styles.actionButton}>
              <MaterialCommunityIcons name="file-plus-outline" color={'#333'} size={15} />
            </Pressable>
          </View>
        </View>
        {navigator.screens?.entrySeq().map(([name, screenOrNav]) => {
          const screenPathSelector = pathSelector.slice();
          screenPathSelector.push(name);
          return screenOrNav.type === 'navigator' ? (
            <NavigationEditorNav
              navigator={screenOrNav}
              key={screenOrNav.name}
              pathSelector={screenPathSelector}
              pages={pages}
            />
          ) : (
            <NavigationEditorScreen
              screen={screenOrNav}
              key={screenOrNav.name}
              pathSelector={screenPathSelector}
              handleNavigationReordering={handleNavigationReordering}
              pages={pages}
            />
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginLeft: 15,
    marginTop: 5,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    borderColor: '#aaa',
    borderLeftWidth: 0.5,
    borderStyle: 'dashed',
  },
  displayRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    marginBottom: 5,
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },
  containerRowRight: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  actionButton: {
    margin: 4,
  },
});

export default NavigationEditorNav;
