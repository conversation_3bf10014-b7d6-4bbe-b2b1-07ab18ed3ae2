import Immutable from 'immutable';
import {isEmpty} from 'lodash';
import React from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {useDispatch} from 'react-redux';
import {Icon, PageConfig, ScreenConfigParams} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import {getNavigationContext} from 'apptile-core';
import {EDITOR_SELECT_NAV_COMPONENT, EDITOR_SELECTED_PAGE_TYPE} from '../../../actions/editorActions';
import ScreenAttachments from './ScreenAttachments';
import {editorSetActiveAttachmentId, editorSetActiveAttachmentKey} from '../../../../web/actions/editorActions';

interface NavigationEditorScreenProps {
  screen: ScreenConfigParams;
  pathSelector: string[];
  handleNavigationReordering?: (operation: string, path: string[]) => {};
  pages: PageConfig;
}

const NavigationEditorScreen: React.FC<NavigationEditorScreenProps> = ({
  screen,
  pathSelector,
  handleNavigationReordering,
  pages,
}) => {
  const dispatch = useDispatch();
  const [showReorderingIcons, setReorderingIcons] = React.useState(false);
  const [selectedPageConfig, setSelectedPageConfig] = React.useState({header: Immutable.Map(), screen: ''});

  const navCallBack = (screen: any) => {
    const context = getNavigationContext();
    const isScreen = screen.type === 'screen';
    const navRoute = isScreen ? screen.name : pathSelector[pathSelector.length - 1];
    isScreen && setActiveAttachment('', '');
    context.navigate(navRoute);
    dispatch({
      type: EDITOR_SELECT_NAV_COMPONENT,
      payload: pathSelector,
    });
    dispatch({
      type: EDITOR_SELECTED_PAGE_TYPE,
      payload: screen.type,
    });
  };

  const setActiveAttachment = (attachmentKey: string, attachmentId: string) => {
    dispatch(editorSetActiveAttachmentId(attachmentId));
    dispatch(editorSetActiveAttachmentKey(attachmentKey));
  };

  const handleReorderingIcons = (screenName: string, event: string) => {
    return () => {
      if (screenName === screen.name && event === 'enter') {
        setReorderingIcons(true);
      } else if (event === 'leave') {
        setReorderingIcons(false);
      }
    };
  };

  const handleAttachmentClick = () => {
    if (selectedPageConfig.screen === screen.screen) {
      setSelectedPageConfig({header: Immutable.Map(), screen: ''});
    } else if (screen.header) {
      const pageConfig = pages.get(screen.header);
      setSelectedPageConfig({header: pageConfig, screen: screen.screen});
    }
  };

  const iconName = selectedPageConfig.screen === screen.screen ? 'menu-up' : 'menu-down';

  return (
    <View style={styles.container}>
      <div
        style={{height: 'auto', flex: 1}}
        onMouseEnter={handleReorderingIcons(screen.name, 'enter')}
        onMouseLeave={handleReorderingIcons(screen.name, 'leave')}>
        <View style={styles.pressableContainer}>
          <Pressable onPress={() => navCallBack(screen)} style={styles.containerRow}>
            {!!screen.header && (
              <MaterialCommunityIcons name={iconName} color={'#333'} size={14} onPress={handleAttachmentClick} />
            )}
            <Icon iconType="Ionicons" name="document-outline" color={'#333'} size={14} />
            <Text style={{marginLeft: 4}} numberOfLines={1}>
              {screen.name}: <Text style={styles.textBubble}>{screen.screen}</Text>
            </Text>
            {showReorderingIcons && (
              <View style={styles.iconContainer}>
                <MaterialCommunityIcons
                  name="arrow-up"
                  color={'#333'}
                  size={18}
                  onPress={handleNavigationReordering('UP', pathSelector)}
                  style={styles.upIcon}
                />
                <MaterialCommunityIcons
                  name="arrow-down"
                  color={'#333'}
                  size={18}
                  onPress={handleNavigationReordering('DOWN', pathSelector)}
                  style={styles.downIcom}
                />
              </View>
            )}
          </Pressable>
        </View>
        {selectedPageConfig.screen && (
          <ScreenAttachments screen={selectedPageConfig.header} onAttachmentClick={navCallBack} />
        )}
      </div>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 5,
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  pressableContainer: {
    display: 'flex',
    flexDirection: 'column',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },
  textBubble: {
    fontSize: 10,
    borderRadius: 2,
    padding: 2,
    backgroundColor: '#98bbf5',
  },
  iconContainer: {
    flex: 1,
    flexDirection: 'row',
    position: 'absolute',
    right: 0,
  },
  upIcon: {
    borderRadius: 40,
    backgroundColor: 'rgb(250,250,250)',
  },
  downIcom: {
    borderRadius: 40,
    backgroundColor: 'rgb(252,252,252)',
    marginLeft: 4,
  },
});

export default NavigationEditorScreen;
