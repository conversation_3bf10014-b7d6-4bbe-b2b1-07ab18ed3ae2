import {pageConfigsSelector} from 'apptile-core';
import {editorClearPageModelCaches, editorGeneratePageModelCaches} from '@/root/web/actions/editorActions';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {DispatchActions} from 'apptile-core';
import CollapsiblePanel from '../../../components/CollapsiblePanel';
import PagesListItemControl from './PagesListItemControl';
import {selectPageConfig} from '../../../actions/editorActions';
import {bindActionCreators} from 'redux';
import {useTheme} from '@/root/app/styles/theme/context';
import {useLoadedFonts} from '@/root/app/fonts/context';
import Button from '@/root/web/components-v2/base/Button';

const PagesListControl: React.FC<{}> = () => {
  const dispatch = useDispatch();
  const {selectPageConfig: selectPage} = useMemo(() => bindActionCreators({selectPageConfig}, dispatch), [dispatch]);

  const pageConfigs = useSelector(pageConfigsSelector);
  const [pages, setPages] = useState(pageConfigs?.sortBy((v, k) => k));
  useEffect(() => {
    setPages(pageConfigs?.sortBy((v, k) => k));
  }, [pageConfigs]);

  const addPage = useCallback(() => {
    const newPageId = prompt('Enter name of the new page');
    if (newPageId) dispatch({type: DispatchActions.ADD_NEW_PAGE, payload: {pageId: newPageId}});
    else dispatch({type: DispatchActions.ADD_NEW_PAGE});
  }, [dispatch]);

  const genCaches = useCallback(() => {
    dispatch(editorGeneratePageModelCaches());
  }, [dispatch]);

  const clearCaches = useCallback(() => {
    dispatch(editorClearPageModelCaches());
  }, [dispatch]);

  const builder = global.BUILDER_EDITOR;

  return (
    <>
      {pages && (
        <View style={styles.container}>
          <View style={{height: 'auto', flex: 1}}>
            <CollapsiblePanel
              key={'Pages'}
              iconName={'document-text-outline'}
              iconType={'Ionicons'}
              title="Pages"
              isOpen={false}>
              <View style={{paddingLeft: 6, paddingRight: 4}}>
                {[...pages?.entries()].map(([pageId, pageConfig]) => {
                  return <PagesListItemControl key={pageId} {...{pageId, selectPageConfig: selectPage, pageConfig}} />;
                })}
                <Button onPress={addPage} color="PRIMARY" size="EXTRA-SMALL" variant="TEXT">
                  Add Page
                </Button>
                {!builder && (
                  <>
                    <Button onPress={genCaches} color="PRIMARY" size="EXTRA-SMALL" variant="TEXT">
                      Generate Caches
                    </Button>
                    <Button onPress={clearCaches} color="PRIMARY" size="EXTRA-SMALL" variant="TEXT">
                      Clear Caches
                    </Button>
                  </>
                )}
              </View>
            </CollapsiblePanel>
          </View>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    paddingHorizontal: 5,
    flexBasis: 'auto',
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },

  actionLinkBox: {
    flexDirection: 'row',
    margin: 2,
    borderRadius: 4,
  },
  actionLinkText: {textAlign: 'right', flex: 1, padding: 10, color: '#3c92dc'},
});

export default PagesListControl;
