import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Animated from 'react-native-reanimated';
import {deletePageConfig, Icon, PageConfig} from 'apptile-core';
import theme from '@/root/web/styles-v2/theme';
import { useDispatch, useSelector } from 'react-redux';
import { selectSelectedPageId } from '@/root/web/selectors/EditorSelectors';

interface PagesListItemControlProps {
  pageId: string;
  pageConfig: PageConfig;
  selectPageConfig: (pageId: string) => void;
}

const PagesListItemControl: React.FC<PagesListItemControlProps> = ({
  pageId,
  pageConfig,
  selectPageConfig,
}) => {
  const onSelect = useCallback(() => {
    selectPageConfig(pageId);
  }, [pageId, selectPageConfig]);

  const selectedPageId = useSelector(state => selectSelectedPageId(state));
  const isSelected = selectedPageId === pageId;

  const [cached, setCached] = useState(false);

  const dispatch = useDispatch();

  useEffect(() => {
    // Check if page has cached models (you may need to adjust this based on your PageConfig type)
    setCached(false); // Set to false for now since _cachedPluginModels doesn't exist on PageConfig
  }, [pageConfig]);

  // Handle delete action (you can implement this based on your requirements)
  const handleDelete = useCallback(
    (e: any) => {
      e.stopPropagation();
      console.log('Delete page:', pageId);
      const confirmation = confirm('Are you sure you want to delete this page?');
      if (confirmation) dispatch(deletePageConfig(pageId));
    },
    [dispatch, pageId],
  );

  return (
    <TouchableOpacity onPress={onSelect} style={styles.container}>
      <View style={[styles.itemContainer, isSelected && styles.selectedItem]}>
        {/* Document Icon */}
        <View style={styles.iconContainer}>
          <Icon name="document-outline" iconType="Ionicons" style={[isSelected && styles.selectedText]} size={theme.FONT_SIZE + 1} />
        </View>

        {/* Page Name */}
        <View style={styles.textContainer}>
          <Text style={[styles.pageName, isSelected && styles.selectedText]}>
            {cached ? '*' : ''}
            {pageId}
          </Text>
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity onPress={handleDelete}>
            <Icon name="delete" size={theme.FONT_SIZE + 2} style={[isSelected && styles.selectedText]} />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 4,
    paddingVertical: 2,
    backgroundColor: '#fff',
  },
  itemContainer: {
    paddingHorizontal: 4,
    paddingVertical: 3,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedItem: {
    backgroundColor: '#3b82f622',
  },
  iconContainer: {
    marginRight: 5,
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
  },
  pageName: {
    fontSize: theme.FONT_SIZE + 1,
    color: '#374151',
    fontWeight: '500',
  },
  selectedText: {
    color: '#3b82f6',
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
});

export default PagesListItemControl;
