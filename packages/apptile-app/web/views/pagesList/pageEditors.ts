import {
  PageTransitions,
  PluginEditorsConfig,
  PageEventsList,
  getEventsEditorConfig
} from 'apptile-core';

export const pageDefaultEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'pageParamsEditor',
      name: 'pageParams',
      props: {
        label: 'Page Parameters',
      },
    },
    {
      type: 'exportPageDialog',
      name: 'exportPage',
      hidden: () => global.BUILDER_EDITOR ?? false,
      props: {
        label: 'Export Page',
      },
    },
    {
      type: 'radioGroup',
      name: 'containerType',
      props: {
        label: 'Container Type',
        options: ['View', 'ScrollView'],
      },
    },
    {
      defaultValue: 'Screen',
      type: 'radioGroup',
      name: 'type',
      props: {
        label: 'Screen Type',
        options: ['Screen', 'Header', 'Loader'],
      },
    },
    {
      type: 'checkbox',
      name: 'disableSafeArea',
      props: {
        label: 'Disable Safe Area Constraints',
      },
    },
    {
      type: 'checkbox',
      name: 'disableBackground',
      props: {
        label: 'Disable Default Background Fill',
      },
    },
    {
      type: 'checkbox',
      name: 'disablePageCache',
      props: {
        checkedValue: false,
        label: 'Disable Page Cache',
      },
    },
    {
      type: 'dropDown',
      name: 'transition',
      props: {
        label: 'Transition',
        options: Object.values(PageTransitions),
      },
    },
  ],
  event: [getEventsEditorConfig(PageEventsList)],
  analytics: [
    {
      type: 'analyticsEditor',
      name: 'analytics',
      props: {
        defaultType: 'page',
      },
    },
  ],
};
