import {Provider} from '@shopify/app-bridge-react';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {connect, useDispatch, useSelector} from 'react-redux';
import {Route, Routes, useLocation, useMatch} from 'react-router';
import {
  fetchAppBranches,
  fetchAppIntegrations,
  fetchIntegrationCategories,
  fetchIntegrations,
  fetchMyAddOns,
  fetchMySubscription,
  fetchPlanList,
  fetchVideoAssets,
  platformInit,
  setActiveNotificationProvider,
  setBuildStatus,
  setEditorFeatures,
  setHasOneSignal,
} from '../../actions/editorActions';
import {EditorRootState} from '../../store/EditorRootState';
import PlatformRouter from './PlatformRouter';
import {useAppBridge} from '@shopify/app-bridge-react';
import {Fullscreen} from '@shopify/app-bridge/actions';
import {View} from 'react-native';
import Button from '../../components-v2/base/Button';
import {FullscreenBar} from './components/FullscreenBar';
import {setShopifyState} from '../../actions/shopifyActions';
import _ from 'lodash';
import {planFeaturesList} from 'apptile-core';
import {selectCurrentPlanWithDetails} from '../../selectors/BillingSelector';
import {fetchOnboardingMetadata} from '../../actions/onboardingActions';
import {checkApptileEmailSelector} from '../../selectors/FeatureGatingSelector';
import {builderEmails, whitelistedEmails} from '../../common/featureGatingConstants';
import {BuildManagerApi} from '../../api/BuildApi';

const apiKey = process.env.REACT_APP_SHOPIFY_API_KEY;

const PlatformInit: React.FC = ({}) => {
  const location = useLocation();
  const dispatch = useDispatch();

  const platformState = useSelector((state: EditorRootState) => state.platform);
  const {platformInitialized, platformHost, isEmbeddedInShopify} = platformState;

  const getHost = () => {
    const queryParams: URLSearchParams = new URLSearchParams(location.search);
    return queryParams.get('host') ? queryParams.get('host') : platformHost;
  };

  const host = getHost();
  const orgId = useMatch('/dashboard/:orgId/*')?.params?.orgId ?? '';
  const {appId, forkId, appSaveId} = useSelector((state: EditorRootState) => state.apptile);

  const [currentBuild, setCurrentBuild] = useState<{status: string; latestBuild: any; liveBuild: any}>({
    status: 'idle',
    latestBuild: null,
    liveBuild: null,
  });

  useEffect(() => {
    if (host) {
      dispatch(platformInit(host, 'SHOPIFY', true));
      window.appHost = host;
    }
  }, [dispatch, host]);

  useEffect(() => {
    if (orgId && !host) {
      dispatch(platformInit('', 'SHOPIFY', false, orgId));
    }
  }, [dispatch, orgId]);

  const preloader = document.querySelector('.preloader');
  const hidePreloader = useCallback(
    () => () => {
      if (preloader) preloader.remove();
    },
    [preloader],
  );

  useEffect(() => {
    if (platformInitialized) hidePreloader();
  }, [platformInitialized, hidePreloader]);

  useEffect(() => {
    dispatch(fetchPlanList());
  }, [dispatch]);

  useEffect(() => {
    if (appId) {
      dispatch(fetchMySubscription(appId as string));
      dispatch(fetchMyAddOns(appId as string));
      dispatch(fetchAppIntegrations(appId as string));
      dispatch(fetchIntegrations());
      dispatch(fetchIntegrationCategories());
      dispatch(fetchOnboardingMetadata(appId as string));
      dispatch(fetchAppBranches(appId as string, forkId));
      dispatch(fetchVideoAssets());

      const fetchBuilds = async () => {
        try {
          const liveBuild = await BuildManagerApi.getBuilds(appId);
          // console.log('Live Build Response:', liveBuild);
          const response = await BuildManagerApi.listBuild(appId, 30);
          const builds = response.data || [];

          if (!builds) {
            throw new Error('No builds response found');
          }
          // If there are no builds, show the button
          if (builds.length === 0) {
            setCurrentBuild({status: 'no-builds', latestBuild: null});
            return;
          }

          // Check if any build (iOS or Android) was created before the cutoff date
          const latestBuilds = builds
            ?.filter((e: any) => e.status == 'completed')
            ?.sort((build1: any, build2: any) => new Date(build2.createdAt) - new Date(build1.createdAt));
          // Only show the button if there are no builds before the cutoff date
          setCurrentBuild({
            status: 'ready',
            latestBuild: {
              ios: latestBuilds.find((e: any) => e.platformType === 'ios'),
              android: latestBuilds.find((e: any) => e.platformType === 'android'),
            },
            liveBuild: {
              ios: liveBuild.data?.ios?.messages?.success?.length
                ? {
                    status: 'success',
                    build:
                      liveBuild.data?.ios?.semVersion == null
                        ? {
                            buildSourceGitHeadName: 'latest',
                          }
                        : latestBuilds.find(
                            (e: any) => e.platformType === 'ios' && e.semVersion == liveBuild.data?.ios?.semVersion,
                          ),
                  }
                : {status: 'error'},
              android: liveBuild.data?.android?.messages?.success?.length
                ? {
                    status: 'success',
                    build:
                      liveBuild.data?.ios?.semVersion == null
                        ? {
                            buildSourceGitHeadName: 'latest',
                          }
                        : latestBuilds.find(
                            (e: any) =>
                              e.platformType === 'android' && e.semVersion == liveBuild.data?.android?.semVersion,
                          ),
                  }
                : {status: 'error'},
            },
          });
        } catch (error) {
          console.error('Error fetching builds:', error);
          // In case of error, default to hiding the button
          setCurrentBuild({status: 'error', latestBuild: null, liveBuild: null});
        }
      };
      fetchBuilds();
    }
  }, [appId, forkId, dispatch]);

  //Check if appintegrations has onesignal
  const appIntegrationsById = useSelector((state: EditorRootState) => state.integration.appIntegrationsById) ?? {};
  //getting all the integrations whose category is 'notification'
  const notificationIntegrations = Object.values(appIntegrationsById).filter(
    integration => integration.category === 'Notifications',
  );
  //getting the active notification integrations
  const activeNotificationIntegrations = notificationIntegrations.filter(
    integration => integration.appIntegrations[0].active,
  );
  //If the active list doesnot contain oneSignal then redirect to NotificationUnavailable page
  const activeNotificationIntegrationCode = activeNotificationIntegrations[0]?.integrationCode;
  useEffect(() => {
    if (activeNotificationIntegrations.length === 0) {
      dispatch(setActiveNotificationProvider('APPTILE'));
    }
    if (activeNotificationIntegrations.length > 0) {
      dispatch(setActiveNotificationProvider(activeNotificationIntegrationCode));
      if (activeNotificationIntegrationCode === 'oneSignal') {
        dispatch(setHasOneSignal(appId as string));
      }
    }
  }, [dispatch, activeNotificationIntegrationCode]);

  const currentPlan = useSelector(selectCurrentPlanWithDetails);

  const basePlan = currentPlan?.basePlan ?? currentPlan;

  let editorFeatures =
    _.find(planFeaturesList, (e: any) => e.serverCode === basePlan?.name)?.allowedFeatures ??
    planFeaturesList.NONE.allowedFeatures;

  const {userFetched, user} = useSelector((state: EditorRootState) => state.user);
  const isApptileUser = useSelector(checkApptileEmailSelector);

  if (userFetched && user?.email && typeof user?.email === 'string') {
    if (isApptileUser || whitelistedEmails.includes(user?.email) || builderEmails.includes(user?.email)) {
      editorFeatures = planFeaturesList.ENTERPRISE.allowedFeatures;
    }
  }

  useEffect(() => {
    dispatch(setEditorFeatures(editorFeatures));
    dispatch(setBuildStatus(currentBuild));
  }, [dispatch, editorFeatures, currentBuild]);

  const routes = (
    <Routes>
      <Route path="*" element={<PlatformRouter />} />
    </Routes>
  );

  return (
    <>
      {isEmbeddedInShopify && (
        <Provider
          config={{
            apiKey: apiKey,
            host: host,
            forceRedirect: true,
          }}>
          {platformInitialized && host ? <ShopifyFullScreenControl host={host} /> : <></>}
          {platformInitialized && routes}
        </Provider>
      )}
      {!isEmbeddedInShopify && <>{routes}</>}
    </>
  );
};

const ShopifyFullScreenControl = ({host}) => {
  const app = useRef<any>(useAppBridge());
  const shopifyFullScreen = useRef<any>();
  const dispatch = useDispatch();
  const [isFullScreen, SetIsFullScreen] = useState<boolean>(false);

  // const fullscreen = Fullscreen.create(app);
  useEffect(() => {
    // setShopifyBridge(app);
    // setShopifyFullScreen(fullscreen);
    shopifyFullScreen.current = Fullscreen.create(app.current);
    const unsubscribeEnter = shopifyFullScreen.current?.subscribe(Fullscreen.Action.ENTER, () => {
      SetIsFullScreen(true);
    });
    const unsubscribeExit = shopifyFullScreen.current?.subscribe(Fullscreen.Action.EXIT, () => {
      SetIsFullScreen(false);
    });
    return () => {
      unsubscribeEnter();
      unsubscribeExit();
    };
  }, [app]);

  const setFullScreen = () => {
    shopifyFullScreen.current?.dispatch(isFullScreen ? Fullscreen.Action.EXIT : Fullscreen.Action.ENTER);
  };

  useEffect(() => {
    dispatch(setShopifyState({isFullscreen: isFullScreen}));
  }, [dispatch, isFullScreen]);

  useEffect(() => {
    if (window.platformConfigs?.requestedFullScreen) {
      SetIsFullScreen(true);
      shopifyFullScreen.current?.dispatch(Fullscreen.Action.ENTER);
    }
  }, []);

  return app ? (
    <>
      {isFullScreen ? (
        <FullscreenBar setFullScreen={setFullScreen} isFullScreen={isFullScreen} />
      ) : (
        <View style={[{position: 'absolute', bottom: 0, left: 0, zIndex: 1}]}>
          <Button
            containerStyles={{backgroundColor: '#FFF', borderColor: 'transparent'}}
            variant="PILL"
            color="SECONDARY"
            size="LARGE"
            icon={isFullScreen ? 'fullscreen-exit' : 'fullscreen'}
            onPress={() => setFullScreen()}>
            {/* {isFullScreen ? 'Exit Fullscreen' : 'Enter Fullscreen} */}
          </Button>
          {/* <FullscreenModal isFullScreen={isFullScreen} setFullScreen={setFullScreen} /> */}
        </View>
      )}
    </>
  ) : null;
};

export default connect()(PlatformInit);
