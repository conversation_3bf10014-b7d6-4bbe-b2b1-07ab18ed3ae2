import Immutable from 'immutable';
import React, {useCallback, useMemo, useState} from 'react';
import {DropTargetMonitor, useDrop} from 'react-dnd';
import {findNodeHandle, Pressable, StyleSheet, View, Text} from 'react-native';
import {useDispatch} from 'react-redux';
import {DragDropItemTypes} from 'apptile-core';
import {AppConfig, PluginConfigType} from 'apptile-core';
import {Selector} from 'apptile-core';
import CollapsiblePanel from '../../../components/CollapsiblePanel';
import PluginListItemControl from './PluginListItemControl';
import {useCallbackRef} from 'apptile-core';
import {isMoveWidget} from 'apptile-core';
import {throttle} from 'lodash';
import {WidgetTree} from 'apptile-core';
import PluginListTreeItem from './PluginListTreeItem';
import {PluginListModuleItemsContext} from './PluginListContext';
import {MaterialCommunityIcons} from 'apptile-core';

interface PluginListControlProps {
  pageId: string;
  pageKey: string;
  appConfig: AppConfig;
  selectedPluginConfig: PluginConfigType<any>;
  selectedPluginSelector?: Selector | null;
  widgetTree: WidgetTree;
  selectPlugin: (pluginSel: string[]) => void;
}

const PluginListControl: React.FC<PluginListControlProps> = ({
  pageId,
  pageKey,
  appConfig,
  selectedPluginConfig,
  selectedPluginSelector,
  widgetTree,
  selectPlugin,
}) => {
  const pluginId = selectedPluginConfig?.id;

  if (!appConfig) return null;
  const globalPlugins = appConfig?.get('plugins');
  const pagePlugins = pageId
    ? appConfig?.getIn(['pages', pageId, 'plugins'], Immutable.OrderedMap())
    : Immutable.OrderedMap();

  const widgets = [...pagePlugins?.entries()].filter(([_, pluginConfig]) => {
    return pluginConfig?.get('type') !== 'query';
  });

  const queries = [...pagePlugins?.entries()].filter(([_, pluginConfig]) => {
    return pluginConfig?.get('type') === 'query';
  });

  const [showModuleItems, setShowModuleItems] = useState(true);

  const hoverCallback = useCallbackRef((item: any, monitor: DropTargetMonitor): any => {
    if (monitor.isOver({shallow: true})) {
      if (isMoveWidget(item)) return;
      if (!monitor.canDrop()) return;
      logger.info(`Global Plugins hover: `, item.payload.pluginType);
    }
  });
  const throttledHoverCallback = useMemo(() => throttle(hoverCallback, 66), [hoverCallback]);
  const throttledHover = useCallback((item, monitor) => {
    throttledHoverCallback(item, monitor);
  }, []);
  const [{isOver}, dropRef] = useDrop({
    accept: DragDropItemTypes.WIDGET,
    hover: throttledHover,
    canDrop: (item, monitor) => {
      if (item && item.payload?.configType === 'widget') return false;
      return true;
    },
    collect: monitor => ({
      isOver: !!monitor.isOver(),
    }),
  });
  const setDragRefs = useCallback(ref => {
    dropRef(ref && findNodeHandle(ref));
  }, []);

  return globalPlugins ? (
    <View style={styles.container}>
      <View style={styles.integrationContainer}>
        <CollapsiblePanel
          ref={setDragRefs}
          key={'Global'}
          iconName="extension-puzzle-outline"
          iconType="Ionicons"
          title="Global Plugins"
          isOpen={false}>
          {[...globalPlugins?.entries()]
            .sort(e => {})
            .map(([_, pluginConfig]) => {
              const pluginSelector = [pluginConfig.id];
              return <PluginListItemControl key={pluginConfig.id} {...{pluginSelector, selectPlugin, pluginConfig}} />;
            })}
        </CollapsiblePanel>

        {/* {pageId && (
          <CollapsiblePanel key={pageId + 'widget'} title="Page Queries" isOpen={true}>
            <Pressable style={styles.actionLinkBox} onPress={onAddPlugin}>
              <Text style={styles.actionLinkText}>Add Query</Text>
            </Pressable>
            {[...queries].map(([_, pluginConfig]) => {
              const pluginSelector = [pageKey, 'plugins', pluginConfig.id];
              return <PluginListItemControl key={pluginConfig.id} {...{pluginSelector, selectPlugin, pluginConfig}} />;
            })}
          </CollapsiblePanel>
        )} */}
        {/* {pageId && widgets && (
          <CollapsiblePanel key={pageId + 'query'} title="Page Widgets" isOpen={true}>
            {[...widgets].map(([_, pluginConfig]) => {
              const pluginSelector = [pageKey, 'plugins', pluginConfig.id];
              return <PluginListItemControl key={pluginConfig.id} {...{pluginSelector, selectPlugin, pluginConfig}} />;
            })}
          </CollapsiblePanel>
        )} */}

        {pageId && widgetTree && (
          <CollapsiblePanel key={pageId + 'query'} title="Page Plugins" iconName="view-dashboard-outline" isOpen={true}>
            <Pressable style={styles.tilesHeader} onPress={() => setShowModuleItems(!showModuleItems)}>
              <MaterialCommunityIcons
                color={showModuleItems ? 'green' : 'amber'}
                name={showModuleItems ? 'alpha-t-circle' : 'alpha-t-circle-outline'}
                size={14}
              />
            </Pressable>
            <PluginListModuleItemsContext.Provider value={showModuleItems}>
              {widgetTree.map(widgetTreeNode => {
                const pluginSelector = [pageKey, 'plugins', widgetTreeNode.id];
                return (
                  <PluginListTreeItem
                    widgetTreeNode={widgetTreeNode}
                    key={widgetTreeNode?.id}
                    pluginSelector={pluginSelector}
                    parentId={''}
                    selectPlugin={selectPlugin}
                    pageId={pageId}
                    selectedPluginId={pluginId}
                  />
                );
              })}
            </PluginListModuleItemsContext.Provider>
          </CollapsiblePanel>
        )}
      </View>
    </View>
  ) : (
    <></>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 5,
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  tilesHeader: {
    flex: 0,
    flexBasis: 'auto',
    maxHeight: 20,
  },
  integrationStyle: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  integrationContainer: {
    height: 'auto',
    flex: 1,
  },
});

export default PluginListControl;
