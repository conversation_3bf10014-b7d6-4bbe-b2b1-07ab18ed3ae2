import React, {useCallback, useEffect, useState} from 'react';
import {PluginListingItem} from '../../../common/hooks/usePluginListing';
import {DragSourceMonitor, useDrag} from 'react-dnd';
import {Button, findNodeHandle, Pressable, StyleSheet, Text, View} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import {CreateWidget, DragDropItemTypes} from 'apptile-core';
import {PluginSubType, SettingsConfig} from 'apptile-core';
import ApptileWebIcons from '@/root/web/icons/_ApptileWebIcons';
import {useDispatch, useSelector} from 'react-redux';
import {selectModulePluginsInApp} from '@/root/web/selectors/EditorModuleSelectors';
import {getEmptyImage} from 'react-dnd-html5-backend';

import { selectAppSettingsForKey, BrandSettingsTypes } from 'apptile-core';
const BRAND_EXPOSED_TILES = BrandSettingsTypes.BRAND_EXPOSED_TILES,
  BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY;
import {updateSettingsValue} from 'apptile-core';

const IMAGE_SIZE = 30;
const ICON_SIZE = 20;
interface PluginListingItemProps extends PluginListingItem {
  onDragEnd?: (item: unknown, monitor: DragSourceMonitor) => void;
  saveModuleTemplate: (moduleUUID: string) => void;
  deleteModuleTemplate: (moduleUUID: string) => void;
  duplicateModuleTemplate: (moduleUUID: string) => void;
}

const PluginListingItemControl: React.FC<PluginListingItemProps> = ({
  pluginType,
  onDragEnd,
  saveModuleTemplate,
  deleteModuleTemplate,
  duplicateModuleTemplate,
  layout,
  ...plugin
}) => {
  const isModule = !!plugin?.moduleUUID;
  const [, dragRef, connectDragPreview] = useDrag({
    type: DragDropItemTypes.WIDGET,
    item: () => {
      const modulePayload = plugin.moduleUUID ? {moduleUUID: plugin.moduleUUID} : {};
      const dragItem: CreateWidget = {
        type: 'widget',
        action: 'create',
        payload: {
          layout,
          pluginType: pluginType as PluginSubType,
          configType: plugin.type,
          ...modulePayload,
        },
      };
      logger.info('Begin Drag: ', dragItem);
      return dragItem;
    },
    end: onDragEnd,
  });
  const setDragRefs = useCallback(ref => {
    dragRef(ref && findNodeHandle(ref));
  }, []);

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const brandExposedTiles = brandSettings.getSettingValue(BRAND_EXPOSED_TILES) ?? '';
  const [exposedModules, setExposedModules] = useState<string[]>([]);

  const pageModules = useSelector(selectModulePluginsInApp);
  const [canDeleteModule, setCanDeleteModule] = useState(false);
  const dispatch = useDispatch();
  useEffect(() => {
    if (isModule) {
      const modulePlugins = pageModules.filter(
        pluginConfig => pluginConfig?.config?.get('moduleUUID') === plugin?.moduleUUID,
      );
      if (modulePlugins.count()) setCanDeleteModule(false);
      else setCanDeleteModule(true);
    }
  }, [isModule, pageModules, plugin?.moduleUUID]);
  useEffect(() => {
    connectDragPreview(getEmptyImage(), {captureDraggingState: true});
  }, [connectDragPreview]);

  useEffect(() => {
    setExposedModules(brandExposedTiles?.split(',')?.filter(e => e) ?? []);
  }, [brandExposedTiles]);

  const onModuleSave = useCallback(() => {
    saveModuleTemplate(plugin.moduleUUID);
  }, [plugin.moduleUUID, saveModuleTemplate]);

  const onModuleDelete = useCallback(() => {
    deleteModuleTemplate(plugin.moduleUUID);
    if (exposedModules?.indexOf(plugin.moduleUUID) > -1) {
      exposedModules.splice(exposedModules.indexOf(plugin.moduleUUID), 1);
      dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_EXPOSED_TILES, exposedModules?.join(',') ?? ''));
    }
  }, [plugin.moduleUUID, deleteModuleTemplate]);

  const onModuleDuplicate = useCallback(() => {
    duplicateModuleTemplate(plugin.moduleUUID);
  }, [plugin.moduleUUID, duplicateModuleTemplate]);

  const onModuleExpose = useCallback(() => {
    if (exposedModules?.indexOf(plugin.moduleUUID) > -1) {
      const newModules = [...exposedModules];
      newModules.splice(exposedModules.indexOf(plugin.moduleUUID), 1);
      dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_EXPOSED_TILES, newModules?.join(',') ?? ''));
    } else {
      exposedModules.push(plugin.moduleUUID);
      const newModules = [...exposedModules];
      newModules.push(plugin.moduleUUID);
      dispatch(updateSettingsValue(BRAND_SETTINGS_KEY, BRAND_EXPOSED_TILES, exposedModules?.join(',') ?? ''));
    }
  }, [dispatch, exposedModules, plugin.moduleUUID]);

  return (
    <View ref={setDragRefs} style={styles.container}>
      {isModule ? (
        <View style={styles.moduleTop}>
          {/* <MaterialCommunityIcons name="image-outline" color="#808080" size={IMAGE_SIZE - 25} /> */}
          <Text style={[styles.labelText, {fontSize: 10}]}>{plugin.name}</Text>
          <View style={styles.bottomBar}>
            <Pressable onPress={onModuleExpose}>
              <MaterialCommunityIcons
                name={`alpha-m-circle${exposedModules.indexOf(plugin.moduleUUID) > -1 ? '' : '-outline'}`}
                color="#777"
                size={16}
              />
            </Pressable>
            <Pressable onPress={onModuleDuplicate}>
              <MaterialCommunityIcons name="content-copy" color="#777" size={16} />
            </Pressable>
            {canDeleteModule && (
              <Pressable onPress={onModuleDelete}>
                <MaterialCommunityIcons name="delete" color="#777" size={16} />
              </Pressable>
            )}
            {plugin.moduleUUID && (
              <Pressable onPress={onModuleSave}>
                <MaterialCommunityIcons name="export" color="#777" size={16} />
              </Pressable>
            )}
          </View>
        </View>
      ) : (
        <>
          {plugin.icon ? (
            <ApptileWebIcons style={styles.iconImage} name={plugin.icon} size={ICON_SIZE} />
          ) : (
            <MaterialCommunityIcons name="image-outline" color="#808080" size={IMAGE_SIZE} />
          )}
          <Text style={[styles.labelText, {fontSize: 11}]}>{plugin.name}</Text>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 0,
    width: 80,
    minHeight: 50,
    flexBasis: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
  },
  iconImage: {
    width: ICON_SIZE,
    height: ICON_SIZE,
    margin: 6,
    textAlign: 'center',
    color: '#505050',
  },
  labelText: {
    textAlign: 'center',
    wordBreak: 'break-word',
  },
  moduleTop: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 4,
    height: '100%',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 2,
  },
  bottomBar: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    justifyContent: 'center',
    marginTop: 4,
    gap: 4,
  },
});

export default PluginListingItemControl;
