import _ from 'lodash';
import {createSelector} from 'reselect';
import {createDeepEqualSelector} from '../common/utils';
import {RootState} from '../store/RootReducer';

export const activeNavigationSelector = (state: RootState) => state.activeNavigation;

export const selectScreens = createSelector(
  (state: RootState) => state.appModel.getIn(['jsModel', 'ApptileNavigation']),
  (navConfig: any) => {
    const screens: any = {};
    const findScreens = (conf: any) => {
      if (conf?.screens) {
        Object.values(conf.screens).forEach(s => findScreens(s));
      } else screens[conf?.name] = conf;
    };
    findScreens(navConfig);
    return screens;
  },
);
