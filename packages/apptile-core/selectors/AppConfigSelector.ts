import Immutable from 'immutable';
import _ from 'lodash';
import {createSelector} from 'reselect';
import {AppConfig, PageConfig, PluginConfig} from '../common/datatypes/types';
import {createImmutableEqualitySelector} from '../common/utils';
import {RootState} from '../store/RootReducer';
import {AppConfigReduxState} from '../store/AppConfigReducer';

export const selectAppConfig = createSelector<RootState, AppConfig>(
  (state: RootState) => state.appConfig.current,
  (appConfig: AppConfig) => appConfig,
);
export const globalPluginsSelector = createSelector(selectAppConfig, appConfig => appConfig?.plugins);
export const pageConfigsSelector = createSelector(selectAppConfig, appConfig => appConfig?.pages);
export const navConfigSelector = createSelector(selectAppConfig, appConfig => appConfig?.navigation);
export const themeConfigSelector = createSelector(selectAppConfig, appConfig => appConfig?.theme);
export const pagePluginsSelector = createSelector(pageConfigsSelector, (pages: Immutable.Map<string, PageConfig>) =>
  pages?.reduce((pluginList, page) => {
    return pluginList.merge(page.plugins?.toList());
  }, Immutable.List<PluginConfig>()),
);

export const EMPTY_FONTS_CONFIG = {};
export const appFontsSelector = createSelector(
  selectAppConfig,
  (appConfig: AppConfig) => appConfig?.getFonts()?.toJS() ?? EMPTY_FONTS_CONFIG,
  {
    memoizeOptions: {
      resultEqualityCheck: _.isEqual,
    },
  },
);

export const appConfigReduxStateSelector = createSelector(
  (state: RootState) => state.appConfig,
  (appConfigReduxState: AppConfigReduxState) => {
    return {isFetching: appConfigReduxState.isFetching, isFetched: appConfigReduxState.isFetched};
  },
  {
    memoizeOptions: {
      resultEqualityCheck: _.isEqual,
    },
  },
);

export const selectGlobalPlugins = createSelector(
  selectAppConfig,
  (appConfig: AppConfig) => {
    return appConfig?.getGlobalPlugins() || Immutable.List<PluginConfig>();
  },
  {
    memoizeOptions: {
      maxSize: 2400,
    },
  },
);

export const selectPluginConfig = createSelector(
  selectAppConfig,
  (state: RootState, pageId: string) => pageId,
  (state: RootState, pageId: string, pluginId: string) => pluginId,
  (appConfig: AppConfig, pageId: string, pluginId: string) => {
    // logger.info('selectPluginConfig', pageId, pluginId);
    return pageId ? appConfig.getPage(pageId)?.getPluginId(pluginId) : appConfig.getPlugin(pluginId);
  },
  {
    memoizeOptions: {
      maxSize: 2400,
    },
  },
);

export const doesPluginExistInPage = createSelector(
  selectAppConfig,
  (state: RootState, pageId: string, pluginIds: string[]) => pageId,
  (state: RootState, pageId: string, pluginIds: string[]) => pluginIds,
  (appConfig: AppConfig, pageId: string, pluginIds: string[]) => {
    if (!pageId || !pluginIds || !Array.isArray(pluginIds)) return { exists: false, pluginId: undefined };
    const page = appConfig.getPage(pageId);
    if (!page || !page.plugins) return { exists: false, pluginId: undefined };
    const foundPluginId = pluginIds.find(pluginId => page.plugins.has(pluginId));
    if (foundPluginId) {
      return { exists: true, pluginId: foundPluginId };
    }
    return { exists: false, pluginId: undefined };
  },
  {
    memoizeOptions: {
      maxSize: 2400,
    },
  },
);


export const selectFirstPluginConfig = createSelector(
  selectAppConfig,
  (state: RootState, pageId: string) => pageId,
  (appConfig: AppConfig, pageId: string) => {
    if (pageId) {
      const page = appConfig.getPage(pageId);
        const plugins = page.plugins?.toList?.();
        return plugins && plugins.size > 0 ? plugins.get(0) : undefined;
    }
    const globalPlugins = appConfig.plugins?.toList?.();
    return globalPlugins && globalPlugins.size > 0 ? globalPlugins.get(0) : undefined;
  },
  {
    memoizeOptions: {
      maxSize: 2400,
    },
  },
);

export const selectLoaderPageConfigs = createImmutableEqualitySelector(
  pageConfigsSelector,
  (pages: Immutable.Map<string, PageConfig>) => {
    return pages?.filter((pageConfig, key) => {
      return pageConfig.type === 'Loader';
    });
  },
);
